#!/bin/bash

# Test script for both User and Admin GraphQL endpoints
# Usage: ./scripts/test-graphql-endpoints.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
USER_ENDPOINT="http://localhost:8080/api/dex-agent/graphql"
ADMIN_ENDPOINT="http://localhost:8080/api/dex-agent/admin/graphql"
USER_PLAYGROUND="http://localhost:8080/api/dex-agent/graphql/playground"
ADMIN_PLAYGROUND="http://localhost:8080/api/dex-agent/admin/graphql/playground"

# Default values
JWT_TOKEN=""
API_KEY=""

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if server is running
check_server() {
    print_status $YELLOW "Checking if server is running..."
    
    if curl -s -f "$USER_ENDPOINT/ping" > /dev/null 2>&1; then
        print_status $GREEN "✓ Server is running"
        return 0
    else
        print_status $RED "✗ Server is not running. Please start the server first."
        print_status $YELLOW "Run: make run or make dev"
        exit 1
    fi
}

# Function to test User GraphQL endpoint
test_user_endpoint() {
    print_status $YELLOW "Testing User GraphQL endpoint..."
    
    # Test playground accessibility
    print_status $YELLOW "  - Testing playground accessibility..."
    if curl -s -f "$USER_PLAYGROUND" > /dev/null 2>&1; then
        print_status $GREEN "  ✓ User playground is accessible"
    else
        print_status $RED "  ✗ User playground is not accessible"
    fi
    
    # Test GraphQL endpoint without authentication (should fail)
    print_status $YELLOW "  - Testing endpoint without authentication..."
    response=$(curl -s -X POST "$USER_ENDPOINT" \
        -H "Content-Type: application/json" \
        -d '{"query":"query { agentLevels { id name } }"}' \
        -w "%{http_code}")
    
    if [[ "$response" == *"401"* ]] || [[ "$response" == *"unauthorized"* ]]; then
        print_status $GREEN "  ✓ User endpoint correctly rejects unauthenticated requests"
    else
        print_status $RED "  ✗ User endpoint should reject unauthenticated requests"
    fi
    
    # Test with JWT token if provided
    if [ -n "$JWT_TOKEN" ]; then
        print_status $YELLOW "  - Testing endpoint with JWT authentication..."
        response=$(curl -s -X POST "$USER_ENDPOINT" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $JWT_TOKEN" \
            -d '{"query":"query { agentLevels { id name } }"}')
        
        if [[ "$response" == *"data"* ]]; then
            print_status $GREEN "  ✓ User endpoint accepts valid JWT token"
        else
            print_status $YELLOW "  ? User endpoint response with JWT: $response"
        fi
    else
        print_status $YELLOW "  - Skipping JWT test (no token provided)"
    fi
}

# Function to test Admin GraphQL endpoint
test_admin_endpoint() {
    print_status $YELLOW "Testing Admin GraphQL endpoint..."
    
    # Test playground accessibility
    print_status $YELLOW "  - Testing admin playground accessibility..."
    if curl -s -f "$ADMIN_PLAYGROUND" > /dev/null 2>&1; then
        print_status $GREEN "  ✓ Admin playground is accessible"
    else
        print_status $RED "  ✗ Admin playground is not accessible"
    fi
    
    # Test GraphQL endpoint without authentication (should fail)
    print_status $YELLOW "  - Testing admin endpoint without authentication..."
    response=$(curl -s -X POST "$ADMIN_ENDPOINT" \
        -H "Content-Type: application/json" \
        -d '{"query":"query { adminGetAllTasks { id name } }"}' \
        -w "%{http_code}")
    
    if [[ "$response" == *"401"* ]] || [[ "$response" == *"unauthorized"* ]]; then
        print_status $GREEN "  ✓ Admin endpoint correctly rejects unauthenticated requests"
    else
        print_status $RED "  ✗ Admin endpoint should reject unauthenticated requests"
    fi
    
    # Test with API key if provided
    if [ -n "$API_KEY" ]; then
        print_status $YELLOW "  - Testing admin endpoint with API key authentication..."
        response=$(curl -s -X POST "$ADMIN_ENDPOINT" \
            -H "Content-Type: application/json" \
            -H "x-api-key: $API_KEY" \
            -d '{"query":"query { adminGetAllTasks { id name } }"}')
        
        if [[ "$response" == *"data"* ]]; then
            print_status $GREEN "  ✓ Admin endpoint accepts valid API key"
        else
            print_status $YELLOW "  ? Admin endpoint response with API key: $response"
        fi
    else
        print_status $YELLOW "  - Skipping API key test (no key provided)"
    fi
    
    # Test with wrong authentication method (JWT on admin endpoint)
    if [ -n "$JWT_TOKEN" ]; then
        print_status $YELLOW "  - Testing admin endpoint with JWT (should fail)..."
        response=$(curl -s -X POST "$ADMIN_ENDPOINT" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $JWT_TOKEN" \
            -d '{"query":"query { adminGetAllTasks { id name } }"}' \
            -w "%{http_code}")
        
        if [[ "$response" == *"401"* ]] || [[ "$response" == *"unauthorized"* ]]; then
            print_status $GREEN "  ✓ Admin endpoint correctly rejects JWT authentication"
        else
            print_status $RED "  ✗ Admin endpoint should reject JWT authentication"
        fi
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -j, --jwt-token TOKEN    JWT token for user authentication testing"
    echo "  -k, --api-key KEY        API key for admin authentication testing"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0"
    echo "  $0 --jwt-token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    echo "  $0 --api-key your-admin-api-key"
    echo "  $0 -j eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... -k your-admin-api-key"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -j|--jwt-token)
            JWT_TOKEN="$2"
            shift 2
            ;;
        -k|--api-key)
            API_KEY="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_status $RED "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_status $GREEN "=== GraphQL Endpoints Testing ==="
    echo ""
    
    print_status $YELLOW "Configuration:"
    echo "  User Endpoint: $USER_ENDPOINT"
    echo "  Admin Endpoint: $ADMIN_ENDPOINT"
    echo "  User Playground: $USER_PLAYGROUND"
    echo "  Admin Playground: $ADMIN_PLAYGROUND"
    echo ""
    
    check_server
    echo ""
    
    test_user_endpoint
    echo ""
    
    test_admin_endpoint
    echo ""
    
    print_status $GREEN "=== Testing Complete ==="
    
    if [ -z "$JWT_TOKEN" ] || [ -z "$API_KEY" ]; then
        echo ""
        print_status $YELLOW "Note: For complete testing, provide both JWT token and API key:"
        print_status $YELLOW "  JWT Token: Use 'make jwt-token' to generate one"
        print_status $YELLOW "  API Key: Check your config.yaml or environment variables"
    fi
}

# Run main function
main
