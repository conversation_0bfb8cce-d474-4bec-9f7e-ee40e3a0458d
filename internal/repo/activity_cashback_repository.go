package repo

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// ActivityCashbackRepositoryInterface defines the interface for activity cashback operations
type ActivityCashbackRepositoryInterface interface {
	CreateActivityCashback(ctx context.Context, cashback *model.ActivityCashback) error
	GetActivityCashbackByID(ctx context.Context, id uuid.UUID) (*model.ActivityCashback, error)
	GetActivityCashbackByTransactionID(ctx context.Context, transactionID uint) (*model.ActivityCashback, error)
	GetActivityCashbacksByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ActivityCashback, error)
	GetPendingActivityCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.ActivityCashback, error)
	UpdateActivityCashbackStatus(ctx context.Context, id uuid.UUID, status string) error
	MarkActivityCashbackAsClaimed(ctx context.Context, id uuid.UUID) error
	GetTotalActivityCashbackByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
	GetPendingActivityCashbackByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
}

// ActivityCashbackRepository implements the activity cashback repository interface
type ActivityCashbackRepository struct {
	db *gorm.DB
}

// NewActivityCashbackRepository creates a new activity cashback repository
func NewActivityCashbackRepository() ActivityCashbackRepositoryInterface {
	return &ActivityCashbackRepository{
		db: global.GVA_DB,
	}
}

// CreateActivityCashback creates a new activity cashback record
func (r *ActivityCashbackRepository) CreateActivityCashback(ctx context.Context, cashback *model.ActivityCashback) error {
	return r.db.WithContext(ctx).Create(cashback).Error
}

// GetActivityCashbackByID retrieves an activity cashback by ID
func (r *ActivityCashbackRepository) GetActivityCashbackByID(ctx context.Context, id uuid.UUID) (*model.ActivityCashback, error) {
	var cashback model.ActivityCashback
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("id = ?", id).
		First(&cashback).Error
	if err != nil {
		return nil, err
	}
	return &cashback, nil
}

// GetActivityCashbackByTransactionID retrieves an activity cashback by affiliate transaction ID
func (r *ActivityCashbackRepository) GetActivityCashbackByTransactionID(ctx context.Context, transactionID uint) (*model.ActivityCashback, error) {
	var cashback model.ActivityCashback
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("affiliate_transaction_id = ?", transactionID).
		First(&cashback).Error
	if err != nil {
		return nil, err
	}
	return &cashback, nil
}

// GetActivityCashbacksByUserID retrieves activity cashbacks for a specific user
func (r *ActivityCashbackRepository) GetActivityCashbacksByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ActivityCashback, error) {
	var cashbacks []model.ActivityCashback
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&cashbacks).Error
	return cashbacks, err
}

// GetPendingActivityCashbacksByUserID retrieves pending activity cashbacks for a specific user
func (r *ActivityCashbackRepository) GetPendingActivityCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.ActivityCashback, error) {
	var cashbacks []model.ActivityCashback
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Order("created_at ASC").
		Find(&cashbacks).Error
	return cashbacks, err
}

// UpdateActivityCashbackStatus updates the status of an activity cashback
func (r *ActivityCashbackRepository) UpdateActivityCashbackStatus(ctx context.Context, id uuid.UUID, status string) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": now,
		}).Error
}

// MarkActivityCashbackAsClaimed marks an activity cashback as claimed
func (r *ActivityCashbackRepository) MarkActivityCashbackAsClaimed(ctx context.Context, id uuid.UUID) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     "CLAIMED",
			"claimed_at": now,
			"updated_at": now,
		}).Error
}

// GetTotalActivityCashbackByUserID calculates the total activity cashback amount for a user
func (r *ActivityCashbackRepository) GetTotalActivityCashbackByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal
	}

	err := r.db.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Select("COALESCE(SUM(cashback_amount_usd), 0) as total_amount").
		Where("user_id = ?", userID).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, err
	}

	return result.TotalAmount, nil
}

// GetPendingActivityCashbackByUserID calculates the pending activity cashback amount for a user
func (r *ActivityCashbackRepository) GetPendingActivityCashbackByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal
	}

	err := r.db.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Select("COALESCE(SUM(cashback_amount_usd), 0) as total_amount").
		Where("user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, err
	}

	return result.TotalAmount, nil
}
