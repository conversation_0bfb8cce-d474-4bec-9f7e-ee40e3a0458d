package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
)

// AdminServiceInterface defines the interface for admin operations
type AdminServiceInterface interface {
	// Task Category Management
	CreateTaskCategory(ctx context.Context, category *model.TaskCategory) error
	UpdateTaskCategory(ctx context.Context, category *model.TaskCategory) error
	DeleteTaskCategory(ctx context.Context, categoryID uint) error
	GetTaskCategories(ctx context.Context) ([]model.TaskCategory, error)

	// Task Management
	CreateTask(ctx context.Context, task *model.ActivityTask, adminUserID uuid.UUID) error
	UpdateTask(ctx context.Context, task *model.ActivityTask, adminUserID uuid.UUID) error
	DeleteTask(ctx context.Context, taskID uuid.UUID) error
	GetAllTasks(ctx context.Context) ([]model.ActivityTask, error)
	GetTasksByCategory(ctx context.Context, categoryID uint) ([]model.ActivityTask, error)

	// Tier Management
	CreateTierBenefit(ctx context.Context, benefit *model.TierBenefit) error
	UpdateTierBenefit(ctx context.Context, benefit *model.TierBenefit) error
	DeleteTierBenefit(ctx context.Context, benefitID uint) error
	GetTierBenefits(ctx context.Context) ([]model.TierBenefit, error)

	// User Management
	GetUserTierInfos(ctx context.Context, limit, offset int) ([]model.UserTierInfo, error)
	UpdateUserTierInfo(ctx context.Context, userID uuid.UUID, tierInfo *model.UserTierInfo) error
	ResetUserProgress(ctx context.Context, userID uuid.UUID) error
	BulkUpdateUserTiers(ctx context.Context, userIDs []uuid.UUID, newTier int) error

	// Task Progress Management
	GetTaskProgress(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.UserTaskProgress, error)
	UpdateTaskProgress(ctx context.Context, progressID uuid.UUID, progress *model.UserTaskProgress) error
	BulkResetTaskProgress(ctx context.Context, taskID uuid.UUID) error

	// Analytics and Reports
	GetTaskCompletionStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error)
	GetUserActivityStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error)
	GetTierDistribution(ctx context.Context) (map[int]int, error)
	GetTopUsers(ctx context.Context, limit int) ([]model.UserTierInfo, error)

	// System Operations
	SeedInitialTasks(ctx context.Context) error
	ResetAllDailyTasks(ctx context.Context) error
	ResetAllWeeklyTasks(ctx context.Context) error
	ResetAllMonthlyTasks(ctx context.Context) error
	RecalculateAllUserTiers(ctx context.Context) error
}

// AdminService implements AdminServiceInterface
type AdminService struct {
	categoryRepo      activity_cashback.TaskCategoryRepositoryInterface
	taskRepo          activity_cashback.ActivityTaskRepositoryInterface
	progressRepo      activity_cashback.UserTaskProgressRepositoryInterface
	tierInfoRepo      activity_cashback.UserTierInfoRepositoryInterface
	tierBenefitRepo   activity_cashback.TierBenefitRepositoryInterface
	completionFactory *activity_cashback.TaskCompletionRepositoryFactory
	claimRepo         activity_cashback.ActivityCashbackClaimRepositoryInterface
	service           ActivityCashbackServiceInterface
}

// NewAdminService creates a new AdminService
func NewAdminService() AdminServiceInterface {
	return &AdminService{
		categoryRepo:    activity_cashback.NewTaskCategoryRepository(),
		taskRepo:        activity_cashback.NewActivityTaskRepository(),
		progressRepo:    activity_cashback.NewUserTaskProgressRepository(),
		tierInfoRepo:    activity_cashback.NewUserTierInfoRepository(),
		tierBenefitRepo: activity_cashback.NewTierBenefitRepository(),

		completionFactory: activity_cashback.NewTaskCompletionRepositoryFactory(),
		claimRepo:         activity_cashback.NewActivityCashbackClaimRepository(),
		service:           NewActivityCashbackService(),
	}
}

// CreateTaskCategory creates a new task category
func (s *AdminService) CreateTaskCategory(ctx context.Context, category *model.TaskCategory) error {
	if err := s.categoryRepo.Create(ctx, category); err != nil {
		global.GVA_LOG.Error("Failed to create task category", zap.Error(err))
		return fmt.Errorf("failed to create task category: %w", err)
	}

	global.GVA_LOG.Info("Task category created", zap.String("name", category.Name))
	return nil
}

// UpdateTaskCategory updates an existing task category
func (s *AdminService) UpdateTaskCategory(ctx context.Context, category *model.TaskCategory) error {
	if err := s.categoryRepo.Update(ctx, category); err != nil {
		global.GVA_LOG.Error("Failed to update task category", zap.Error(err))
		return fmt.Errorf("failed to update task category: %w", err)
	}

	global.GVA_LOG.Info("Task category updated", zap.String("name", category.Name))
	return nil
}

// DeleteTaskCategory deletes a task category
func (s *AdminService) DeleteTaskCategory(ctx context.Context, categoryID uint) error {
	if err := s.categoryRepo.Delete(ctx, categoryID); err != nil {
		global.GVA_LOG.Error("Failed to delete task category", zap.Error(err))
		return fmt.Errorf("failed to delete task category: %w", err)
	}

	global.GVA_LOG.Info("Task category deleted", zap.Uint("category_id", categoryID))
	return nil
}

// GetTaskCategories retrieves all task categories
func (s *AdminService) GetTaskCategories(ctx context.Context) ([]model.TaskCategory, error) {
	categories, err := s.categoryRepo.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get task categories: %w", err)
	}
	return categories, nil
}

// CreateTask creates a new task
func (s *AdminService) CreateTask(ctx context.Context, task *model.ActivityTask, adminUserID uuid.UUID) error {
	task.CreatedBy = &adminUserID
	task.UpdatedBy = &adminUserID

	if err := s.taskRepo.Create(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err))
		return fmt.Errorf("failed to create task: %w", err)
	}

	global.GVA_LOG.Info("Task created",
		zap.String("task_id", task.ID.String()),
		zap.String("name", task.Name),
		zap.String("admin_id", adminUserID.String()))
	return nil
}

// UpdateTask updates an existing task
func (s *AdminService) UpdateTask(ctx context.Context, task *model.ActivityTask, adminUserID uuid.UUID) error {
	task.UpdatedBy = &adminUserID
	task.UpdatedAt = time.Now()

	if err := s.taskRepo.Update(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to update task", zap.Error(err))
		return fmt.Errorf("failed to update task: %w", err)
	}

	global.GVA_LOG.Info("Task updated",
		zap.String("task_id", task.ID.String()),
		zap.String("name", task.Name),
		zap.String("admin_id", adminUserID.String()))
	return nil
}

// DeleteTask deletes a task
func (s *AdminService) DeleteTask(ctx context.Context, taskID uuid.UUID) error {
	if err := s.taskRepo.Delete(ctx, taskID); err != nil {
		global.GVA_LOG.Error("Failed to delete task", zap.Error(err))
		return fmt.Errorf("failed to delete task: %w", err)
	}

	global.GVA_LOG.Info("Task deleted", zap.String("task_id", taskID.String()))
	return nil
}

// GetAllTasks retrieves all tasks
func (s *AdminService) GetAllTasks(ctx context.Context) ([]model.ActivityTask, error) {
	tasks, err := s.taskRepo.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get all tasks: %w", err)
	}
	return tasks, nil
}

// GetTasksByCategory retrieves tasks by category
func (s *AdminService) GetTasksByCategory(ctx context.Context, categoryID uint) ([]model.ActivityTask, error) {
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tasks by category: %w", err)
	}
	return tasks, nil
}

// CreateTierBenefit creates a new tier benefit
func (s *AdminService) CreateTierBenefit(ctx context.Context, benefit *model.TierBenefit) error {
	if err := s.tierBenefitRepo.Create(ctx, benefit); err != nil {
		global.GVA_LOG.Error("Failed to create tier benefit", zap.Error(err))
		return fmt.Errorf("failed to create tier benefit: %w", err)
	}

	global.GVA_LOG.Info("Tier benefit created",
		zap.Int("tier_level", benefit.TierLevel),
		zap.String("tier_name", benefit.TierName))
	return nil
}

// UpdateTierBenefit updates an existing tier benefit
func (s *AdminService) UpdateTierBenefit(ctx context.Context, benefit *model.TierBenefit) error {
	if err := s.tierBenefitRepo.Update(ctx, benefit); err != nil {
		global.GVA_LOG.Error("Failed to update tier benefit", zap.Error(err))
		return fmt.Errorf("failed to update tier benefit: %w", err)
	}

	global.GVA_LOG.Info("Tier benefit updated",
		zap.Int("tier_level", benefit.TierLevel),
		zap.String("tier_name", benefit.TierName))
	return nil
}

// DeleteTierBenefit deletes a tier benefit
func (s *AdminService) DeleteTierBenefit(ctx context.Context, benefitID uint) error {
	if err := s.tierBenefitRepo.Delete(ctx, benefitID); err != nil {
		global.GVA_LOG.Error("Failed to delete tier benefit", zap.Error(err))
		return fmt.Errorf("failed to delete tier benefit: %w", err)
	}

	global.GVA_LOG.Info("Tier benefit deleted", zap.Uint("benefit_id", benefitID))
	return nil
}

// GetTierBenefits retrieves all tier benefits
func (s *AdminService) GetTierBenefits(ctx context.Context) ([]model.TierBenefit, error) {
	benefits, err := s.tierBenefitRepo.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tier benefits: %w", err)
	}
	return benefits, nil
}

// GetUserTierInfos retrieves user tier information with pagination
func (s *AdminService) GetUserTierInfos(ctx context.Context, limit, offset int) ([]model.UserTierInfo, error) {
	// This would need a new method in the repository for pagination
	// For now, we'll get top users by points
	tierInfos, err := s.tierInfoRepo.GetTopUsersByPoints(ctx, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get user tier infos: %w", err)
	}
	return tierInfos, nil
}

// UpdateUserTierInfo updates user tier information
func (s *AdminService) UpdateUserTierInfo(ctx context.Context, userID uuid.UUID, tierInfo *model.UserTierInfo) error {
	if err := s.tierInfoRepo.Update(ctx, tierInfo); err != nil {
		global.GVA_LOG.Error("Failed to update user tier info", zap.Error(err))
		return fmt.Errorf("failed to update user tier info: %w", err)
	}

	global.GVA_LOG.Info("User tier info updated", zap.String("user_id", userID.String()))
	return nil
}

// ResetUserProgress resets all progress for a user
func (s *AdminService) ResetUserProgress(ctx context.Context, userID uuid.UUID) error {
	// Get all user progress
	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user progress: %w", err)
	}

	// Reset each progress
	for _, p := range progress {
		p.Reset()
		if err := s.progressRepo.Update(ctx, &p); err != nil {
			global.GVA_LOG.Error("Failed to reset user progress", zap.Error(err))
		}
	}

	global.GVA_LOG.Info("User progress reset", zap.String("user_id", userID.String()))
	return nil
}

// BulkUpdateUserTiers updates tier for multiple users
func (s *AdminService) BulkUpdateUserTiers(ctx context.Context, userIDs []uuid.UUID, newTier int) error {
	for _, userID := range userIDs {
		if err := s.service.UpgradeUserTier(ctx, userID, newTier); err != nil {
			global.GVA_LOG.Error("Failed to update user tier",
				zap.Error(err),
				zap.String("user_id", userID.String()),
				zap.Int("new_tier", newTier))
		}
	}

	global.GVA_LOG.Info("Bulk tier update completed",
		zap.Int("user_count", len(userIDs)),
		zap.Int("new_tier", newTier))
	return nil
}

// SeedInitialTasks seeds initial tasks into the database
func (s *AdminService) SeedInitialTasks(ctx context.Context) error {
	seeder := NewTaskSeeder()
	return seeder.SeedTasks(ctx)
}

// GetTaskProgress retrieves task progress for a specific task
func (s *AdminService) GetTaskProgress(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.UserTaskProgress, error) {
	progress, err := s.progressRepo.GetByTaskID(ctx, taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task progress: %w", err)
	}

	// Apply pagination manually since repository doesn't support it yet
	start := offset
	end := offset + limit
	if start > len(progress) {
		return []model.UserTaskProgress{}, nil
	}
	if end > len(progress) {
		end = len(progress)
	}

	return progress[start:end], nil
}

// UpdateTaskProgress updates task progress
func (s *AdminService) UpdateTaskProgress(ctx context.Context, progressID uuid.UUID, progress *model.UserTaskProgress) error {
	if err := s.progressRepo.Update(ctx, progress); err != nil {
		global.GVA_LOG.Error("Failed to update task progress", zap.Error(err))
		return fmt.Errorf("failed to update task progress: %w", err)
	}

	global.GVA_LOG.Info("Task progress updated", zap.String("progress_id", progressID.String()))
	return nil
}

// BulkResetTaskProgress resets progress for all users on a specific task
func (s *AdminService) BulkResetTaskProgress(ctx context.Context, taskID uuid.UUID) error {
	progress, err := s.progressRepo.GetByTaskID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	for _, p := range progress {
		p.Reset()
		if err := s.progressRepo.Update(ctx, &p); err != nil {
			global.GVA_LOG.Error("Failed to reset task progress", zap.Error(err))
		}
	}

	global.GVA_LOG.Info("Bulk task progress reset",
		zap.String("task_id", taskID.String()),
		zap.Int("progress_count", len(progress)))
	return nil
}

// GetTaskCompletionStats retrieves task completion statistics
func (s *AdminService) GetTaskCompletionStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error) {
	// Get all tasks
	tasks, err := s.taskRepo.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tasks: %w", err)
	}

	stats := make(map[string]interface{})
	taskStats := make(map[string]int)

	unifiedRepo := s.completionFactory.GetUnifiedRepository()
	for _, task := range tasks {
		count, err := unifiedRepo.GetTaskCompletionStats(ctx, task.ID, startDate, endDate)
		if err != nil {
			global.GVA_LOG.Error("Failed to get task completion stats", zap.Error(err))
			continue
		}
		taskStats[task.Name] = count
	}

	stats["task_completions"] = taskStats
	stats["start_date"] = startDate
	stats["end_date"] = endDate
	stats["total_tasks"] = len(tasks)

	return stats, nil
}

// GetUserActivityStats retrieves user activity statistics
func (s *AdminService) GetUserActivityStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error) {
	// Get daily completion stats for the date range
	stats := make(map[string]interface{})
	dailyStats := make(map[string]int)

	// Iterate through each day in the range
	for d := startDate; d.Before(endDate) || d.Equal(endDate); d = d.AddDate(0, 0, 1) {
		unifiedRepo := s.completionFactory.GetUnifiedRepository()
		dayStats, err := unifiedRepo.GetDailyCompletionStats(ctx, d)
		if err != nil {
			global.GVA_LOG.Error("Failed to get daily completion stats", zap.Error(err))
			continue
		}

		totalCompletions := 0
		for _, count := range dayStats {
			totalCompletions += count
		}

		dailyStats[d.Format("2006-01-02")] = totalCompletions
	}

	stats["daily_completions"] = dailyStats
	stats["start_date"] = startDate
	stats["end_date"] = endDate

	return stats, nil
}

// GetTierDistribution retrieves tier distribution statistics
func (s *AdminService) GetTierDistribution(ctx context.Context) (map[int]int, error) {
	distribution := make(map[int]int)

	// Get all tier benefits to know available tiers
	benefits, err := s.tierBenefitRepo.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tier benefits: %w", err)
	}

	// Initialize distribution with zero counts
	for _, benefit := range benefits {
		distribution[benefit.TierLevel] = 0
	}

	// Count users in each tier
	for _, benefit := range benefits {
		users, err := s.tierInfoRepo.GetByTier(ctx, benefit.TierLevel)
		if err != nil {
			global.GVA_LOG.Error("Failed to get users by tier", zap.Error(err))
			continue
		}
		distribution[benefit.TierLevel] = len(users)
	}

	return distribution, nil
}

// GetTopUsers retrieves top users by points
func (s *AdminService) GetTopUsers(ctx context.Context, limit int) ([]model.UserTierInfo, error) {
	users, err := s.tierInfoRepo.GetTopUsersByPoints(ctx, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get top users: %w", err)
	}
	return users, nil
}

// ResetAllDailyTasks resets all daily tasks
func (s *AdminService) ResetAllDailyTasks(ctx context.Context) error {
	return s.service.ResetDailyTasks(ctx)
}

// ResetAllWeeklyTasks resets all weekly tasks
func (s *AdminService) ResetAllWeeklyTasks(ctx context.Context) error {
	return s.service.ResetWeeklyTasks(ctx)
}

// ResetAllMonthlyTasks resets all monthly tasks
func (s *AdminService) ResetAllMonthlyTasks(ctx context.Context) error {
	return s.service.ResetMonthlyTasks(ctx)
}

// RecalculateAllUserTiers recalculates tiers for all users
func (s *AdminService) RecalculateAllUserTiers(ctx context.Context) error {
	// Get all users with tier info
	users, err := s.tierInfoRepo.GetTopUsersByPoints(ctx, 10000) // Get a large number
	if err != nil {
		return fmt.Errorf("failed to get users: %w", err)
	}

	recalculatedCount := 0
	for _, user := range users {
		// Check if user should be upgraded
		newTier, err := s.service.CheckTierUpgrade(ctx, user.UserID)
		if err != nil {
			global.GVA_LOG.Error("Failed to check tier upgrade",
				zap.Error(err),
				zap.String("user_id", user.UserID.String()))
			continue
		}

		if newTier != nil {
			recalculatedCount++
		}
	}

	global.GVA_LOG.Info("Tier recalculation completed",
		zap.Int("total_users", len(users)),
		zap.Int("recalculated", recalculatedCount))

	return nil
}
