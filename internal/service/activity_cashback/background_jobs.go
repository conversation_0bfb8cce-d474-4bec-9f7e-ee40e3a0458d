package activity_cashback

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// BackgroundJobManager manages all background jobs for activity cashback system
type BackgroundJobManager struct {
	scheduler        *SchedulerService
	processorManager *TaskProcessorManager
	service          ActivityCashbackServiceInterface
	adminService     AdminServiceInterface
	isRunning        bool
	stopChan         chan struct{}
	wg               sync.WaitGroup
	mu               sync.RWMutex
}

// NewBackgroundJobManager creates a new BackgroundJobManager
func NewBackgroundJobManager() *BackgroundJobManager {
	service := NewActivityCashbackService()
	adminService := NewAdminService()
	scheduler := NewSchedulerService()
	processorManager := NewTaskProcessorManager(service)

	return &BackgroundJobManager{
		scheduler:        scheduler,
		processorManager: processorManager,
		service:          service,
		adminService:     adminService,
		isRunning:        false,
		stopChan:         make(chan struct{}),
	}
}

// Start starts all background jobs
func (bjm *BackgroundJobManager) Start() error {
	bjm.mu.Lock()
	defer bjm.mu.Unlock()

	if bjm.isRunning {
		global.GVA_LOG.Warn("Background job manager is already running")
		return nil
	}

	bjm.isRunning = true
	global.GVA_LOG.Info("Starting Activity Cashback Background Job Manager")

	// Start the scheduler
	bjm.scheduler.Start()

	// Start individual background jobs
	bjm.wg.Add(5)
	go bjm.runDailyTaskResetJob()
	go bjm.runTierUpgradeJob()
	go bjm.runCashbackProcessingJob()
	go bjm.runMonthlyStatsResetJob()
	go bjm.runTaskProgressCleanupJob()

	global.GVA_LOG.Info("All background jobs started successfully")
	return nil
}

// Stop stops all background jobs
func (bjm *BackgroundJobManager) Stop() {
	bjm.mu.Lock()
	defer bjm.mu.Unlock()

	if !bjm.isRunning {
		return
	}

	global.GVA_LOG.Info("Stopping Activity Cashback Background Job Manager")

	bjm.isRunning = false
	close(bjm.stopChan)

	// Stop the scheduler
	bjm.scheduler.Stop()

	// Wait for all jobs to finish
	bjm.wg.Wait()

	global.GVA_LOG.Info("All background jobs stopped successfully")
}

// IsRunning returns whether the background job manager is running
func (bjm *BackgroundJobManager) IsRunning() bool {
	bjm.mu.RLock()
	defer bjm.mu.RUnlock()
	return bjm.isRunning
}

// runDailyTaskResetJob runs daily task reset at UTC 00:00
func (bjm *BackgroundJobManager) runDailyTaskResetJob() {
	defer bjm.wg.Done()

	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	global.GVA_LOG.Info("Daily task reset job started")

	for {
		select {
		case <-bjm.stopChan:
			global.GVA_LOG.Info("Daily task reset job stopped")
			return
		case <-ticker.C:
			now := time.Now().UTC()
			// Run at 00:00 UTC (with 5-minute window)
			if now.Hour() == 0 && now.Minute() < 5 {
				bjm.performDailyTaskReset()
			}
		}
	}
}

// runTierUpgradeJob checks for tier upgrades every 30 minutes
func (bjm *BackgroundJobManager) runTierUpgradeJob() {
	defer bjm.wg.Done()

	ticker := time.NewTicker(30 * time.Minute)
	defer ticker.Stop()

	global.GVA_LOG.Info("Tier upgrade job started")

	for {
		select {
		case <-bjm.stopChan:
			global.GVA_LOG.Info("Tier upgrade job stopped")
			return
		case <-ticker.C:
			bjm.performTierUpgradeCheck()
		}
	}
}

// runCashbackProcessingJob processes pending cashback claims every 5 minutes
func (bjm *BackgroundJobManager) runCashbackProcessingJob() {
	defer bjm.wg.Done()

	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	global.GVA_LOG.Info("Cashback processing job started")

	for {
		select {
		case <-bjm.stopChan:
			global.GVA_LOG.Info("Cashback processing job stopped")
			return
		case <-ticker.C:
			bjm.processPendingCashbackClaims()
		}
	}
}

// runMonthlyStatsResetJob resets monthly stats on the 1st of each month
func (bjm *BackgroundJobManager) runMonthlyStatsResetJob() {
	defer bjm.wg.Done()

	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	global.GVA_LOG.Info("Monthly stats reset job started")

	for {
		select {
		case <-bjm.stopChan:
			global.GVA_LOG.Info("Monthly stats reset job stopped")
			return
		case <-ticker.C:
			now := time.Now().UTC()
			// Run on the 1st of each month at 01:00 UTC
			if now.Day() == 1 && now.Hour() == 1 && now.Minute() < 5 {
				bjm.performMonthlyStatsReset()
			}
		}
	}
}

// runTaskProgressCleanupJob cleans up expired task progress every 24 hours
func (bjm *BackgroundJobManager) runTaskProgressCleanupJob() {
	defer bjm.wg.Done()

	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	global.GVA_LOG.Info("Task progress cleanup job started")

	for {
		select {
		case <-bjm.stopChan:
			global.GVA_LOG.Info("Task progress cleanup job stopped")
			return
		case <-ticker.C:
			bjm.performTaskProgressCleanup()
		}
	}
}

// performDailyTaskReset performs daily task reset
func (bjm *BackgroundJobManager) performDailyTaskReset() {
	ctx := context.Background()

	global.GVA_LOG.Info("Performing daily task reset")

	if err := bjm.service.ResetDailyTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset daily tasks", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Daily task reset completed successfully")
}

// performTierUpgradeCheck checks for users eligible for tier upgrades
func (bjm *BackgroundJobManager) performTierUpgradeCheck() {
	ctx := context.Background()

	global.GVA_LOG.Debug("Performing tier upgrade check")

	// Get top users who might be eligible for upgrades
	users, err := bjm.adminService.GetTopUsers(ctx, 1000)
	if err != nil {
		global.GVA_LOG.Error("Failed to get top users for tier upgrade check", zap.Error(err))
		return
	}

	upgradeCount := 0
	for _, user := range users {
		newTier, err := bjm.service.CheckTierUpgrade(ctx, user.UserID)
		if err != nil {
			global.GVA_LOG.Error("Failed to check tier upgrade",
				zap.Error(err),
				zap.String("user_id", user.UserID.String()))
			continue
		}

		if newTier != nil {
			upgradeCount++
			global.GVA_LOG.Info("User tier upgraded",
				zap.String("user_id", user.UserID.String()),
				zap.Int("old_tier", user.CurrentTier),
				zap.Int("new_tier", newTier.TierLevel))
		}
	}

	if upgradeCount > 0 {
		global.GVA_LOG.Info("Tier upgrade check completed",
			zap.Int("users_checked", len(users)),
			zap.Int("upgrades_performed", upgradeCount))
	}
}

// processPendingCashbackClaims processes pending cashback claims
func (bjm *BackgroundJobManager) processPendingCashbackClaims() {
	ctx := context.Background()

	claims, err := bjm.service.GetPendingClaims(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending claims", zap.Error(err))
		return
	}

	if len(claims) == 0 {
		return // No pending claims
	}

	global.GVA_LOG.Info("Processing pending cashback claims", zap.Int("count", len(claims)))

	processedCount := 0
	for _, claim := range claims {
		// Process the claim
		if err := bjm.service.ProcessClaim(ctx, claim.ID); err != nil {
			global.GVA_LOG.Error("Failed to process claim",
				zap.Error(err),
				zap.String("claim_id", claim.ID.String()))
			continue
		}

		// Simulate blockchain transaction (in production, integrate with actual blockchain)
		mockTxHash := "0x" + claim.ID.String()[:40]
		if err := bjm.service.CompleteClaim(ctx, claim.ID, mockTxHash); err != nil {
			global.GVA_LOG.Error("Failed to complete claim",
				zap.Error(err),
				zap.String("claim_id", claim.ID.String()))
			continue
		}

		processedCount++
	}

	global.GVA_LOG.Info("Cashback claims processing completed",
		zap.Int("total_claims", len(claims)),
		zap.Int("processed", processedCount))
}

// performMonthlyStatsReset resets monthly statistics
func (bjm *BackgroundJobManager) performMonthlyStatsReset() {
	ctx := context.Background()

	global.GVA_LOG.Info("Performing monthly stats reset")

	if err := bjm.service.ResetMonthlyStats(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset monthly stats", zap.Error(err))
		return
	}

	// Also reset monthly tasks
	if err := bjm.service.ResetMonthlyTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset monthly tasks", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Monthly stats reset completed successfully")
}

// performTaskProgressCleanup cleans up expired task progress
func (bjm *BackgroundJobManager) performTaskProgressCleanup() {
	global.GVA_LOG.Info("Performing task progress cleanup")

	// This would involve cleaning up old task progress records
	// For now, we'll just log that the cleanup is running
	// In production, you might want to:
	// 1. Remove very old completed task progress
	// 2. Clean up progress for deleted tasks
	// 3. Archive old completion history

	global.GVA_LOG.Info("Task progress cleanup completed")
}

// ProcessTradingEvent processes trading events from external systems
func (bjm *BackgroundJobManager) ProcessTradingEvent(ctx context.Context, userID string, tradeData map[string]interface{}) error {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}
	return bjm.processorManager.ProcessTradingEvent(ctx, userUUID, tradeData)
}

// ProcessUserLogin processes user login events
func (bjm *BackgroundJobManager) ProcessUserLogin(ctx context.Context, userID string) error {
	// Process daily check-in
	loginData := map[string]interface{}{
		"login_time": time.Now(),
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	return bjm.processorManager.ProcessTaskByIdentifier(ctx, userUUID, model.TaskIDDailyCheckin, "daily", loginData)
}

// ProcessMarketCheck processes market check events
func (bjm *BackgroundJobManager) ProcessMarketCheck(ctx context.Context, userID string) error {
	marketData := map[string]interface{}{
		"check_time": time.Now(),
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	return bjm.processorManager.ProcessTaskByIdentifier(ctx, userUUID, model.TaskIDMarketPageView, "daily", marketData)
}

// GetJobStatus returns the status of all background jobs
func (bjm *BackgroundJobManager) GetJobStatus() map[string]interface{} {
	bjm.mu.RLock()
	defer bjm.mu.RUnlock()

	return map[string]interface{}{
		"is_running": bjm.isRunning,
		"jobs": []map[string]interface{}{
			{
				"name":        "daily_task_reset",
				"description": "Resets daily tasks at UTC 00:00",
				"interval":    "1 hour check",
			},
			{
				"name":        "tier_upgrade_check",
				"description": "Checks for tier upgrades",
				"interval":    "30 minutes",
			},
			{
				"name":        "cashback_processing",
				"description": "Processes pending cashback claims",
				"interval":    "5 minutes",
			},
			{
				"name":        "monthly_stats_reset",
				"description": "Resets monthly stats on 1st of month",
				"interval":    "1 hour check",
			},
			{
				"name":        "task_progress_cleanup",
				"description": "Cleans up expired task progress",
				"interval":    "24 hours",
			},
		},
		"scheduler_status": bjm.scheduler.GetSchedulerStatus(),
	}
}
