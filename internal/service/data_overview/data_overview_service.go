package data_overview

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// DataOverviewServiceInterface defines the interface for data overview operations
type DataOverviewServiceInterface interface {
	GetDataOverview(ctx context.Context, userID uuid.UUID, dataType string, timeRange string) (*response.DataOverviewResponse, error)
}

// DataOverviewService implements data overview operations
type DataOverviewService struct {
	affiliateRepo   transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo transaction.HyperLiquidTransactionRepositoryInterface
	commissionRepo  transaction.CommissionLedgerRepositoryInterface
	userRepo        transaction.UserRepositoryInterface
}

// NewDataOverviewService creates a new data overview service
func NewDataOverviewService() DataOverviewServiceInterface {
	return &DataOverviewService{
		affiliateRepo:   transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo: transaction.NewHyperLiquidTransactionRepository(),
		commissionRepo:  transaction.NewCommissionLedgerRepository(),
		userRepo:        transaction.NewUserRepository(),
	}
}

// GetDataOverview retrieves data overview based on the specified data type and time range
func (s *DataOverviewService) GetDataOverview(ctx context.Context, userID uuid.UUID, dataType string, timeRange string) (*response.DataOverviewResponse, error) {
	// Get all downline users (up to 3 levels)
	allDownlineUsers, err := s.userRepo.GetAllDownlineUsers(ctx, userID, 3)
	if err != nil {
		global.GVA_LOG.Error("Failed to get downline users", zap.Error(err))
		return nil, fmt.Errorf("failed to get downline users: %w", err)
	}

	// Get data based on time range
	var data []*response.DataOverview
	var startTime, endTime time.Time

	switch timeRange {
	case "ONE_DAY":
		endTime = time.Now()
		startTime = endTime.Add(-24 * time.Hour).UTC()
		data, err = s.getHourlyData(ctx, userID, allDownlineUsers, dataType, startTime, endTime)
	case "THIRTY_DAYS":
		endTime = time.Now()
		startTime = endTime.Add(-30 * 24 * time.Hour).UTC()
		data, err = s.getDailyData(ctx, userID, allDownlineUsers, dataType, startTime, endTime)
	case "SIXTY_DAYS":
		endTime = time.Now()
		startTime = endTime.Add(-60 * 24 * time.Hour).UTC()
		data, err = s.getDailyData(ctx, userID, allDownlineUsers, dataType, startTime, endTime)
	case "ALL_TIME":
		endTime = time.Now()
		startTime = endTime.Add(-12 * 30 * 24 * time.Hour).UTC() // 12 months
		data, err = s.getMonthlyData(ctx, userID, allDownlineUsers, dataType, startTime, endTime)
	default:
		return nil, fmt.Errorf("unsupported time range: %s", timeRange)
	}

	if err != nil {
		return nil, err
	}

	// Calculate summary statistics
	summary := s.calculateSummary(data)

	return &response.DataOverviewResponse{
		Data:    data,
		Summary: summary,
		Success: true,
	}, nil
}

// getHourlyData gets data for the past 24 hours with hourly granularity
func (s *DataOverviewService) getHourlyData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, dataType string, startTime, endTime time.Time) ([]*response.DataOverview, error) {
	var data []*response.DataOverview

	for i := 0; i < 24; i++ {
		periodStart := startTime.Add(time.Duration(i) * time.Hour)
		periodEnd := periodStart.Add(time.Hour)

		rebateAmount, transactionVolume, invitationCount, err := s.getPeriodData(ctx, userID, downlineUsers, dataType, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		data = append(data, &response.DataOverview{
			RebateAmount:      rebateAmount,
			TransactionVolume: transactionVolume,
			InvitationCount:   invitationCount,
			Timestamp:         periodStart,
			Period:            periodStart.Format("15:04"),
		})
	}

	return data, nil
}

// getDailyData gets data for the specified days with daily granularity
func (s *DataOverviewService) getDailyData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, dataType string, startTime, endTime time.Time) ([]*response.DataOverview, error) {
	var data []*response.DataOverview

	days := int(endTime.Sub(startTime).Hours() / 24)
	for i := 0; i < days; i++ {
		periodStart := startTime.AddDate(0, 0, i)
		periodEnd := periodStart.AddDate(0, 0, 1)

		rebateAmount, transactionVolume, invitationCount, err := s.getPeriodData(ctx, userID, downlineUsers, dataType, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		data = append(data, &response.DataOverview{
			RebateAmount:      rebateAmount,
			TransactionVolume: transactionVolume,
			InvitationCount:   invitationCount,
			Timestamp:         periodStart,
			Period:            periodStart.Format("01/02"),
		})
	}

	return data, nil
}

// getMonthlyData gets data for the past 12 months with monthly granularity
func (s *DataOverviewService) getMonthlyData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, dataType string, startTime, endTime time.Time) ([]*response.DataOverview, error) {
	var data []*response.DataOverview

	for i := 0; i < 12; i++ {
		periodStart := startTime.AddDate(0, i, 0)
		periodEnd := periodStart.AddDate(0, 1, 0)

		rebateAmount, transactionVolume, invitationCount, err := s.getPeriodData(ctx, userID, downlineUsers, dataType, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		data = append(data, &response.DataOverview{
			RebateAmount:      rebateAmount,
			TransactionVolume: transactionVolume,
			InvitationCount:   invitationCount,
			Timestamp:         periodStart,
			Period:            periodStart.Format("2006-01"),
		})
	}

	return data, nil
}

// getPeriodData gets data for a specific time period
func (s *DataOverviewService) getPeriodData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, dataType string, startTime, endTime time.Time) (float64, float64, int, error) {
	var rebateAmount, transactionVolume float64
	var invitationCount int

	switch dataType {
	case "ALL":
		// Get MEME data
		memeRebate, memeVolume, err := s.getMemeData(ctx, userID, downlineUsers, startTime, endTime)
		if err != nil {
			return 0, 0, 0, err
		}

		// Get CONTRACT data
		contractRebate, contractVolume, err := s.getContractData(ctx, userID, downlineUsers, startTime, endTime)
		if err != nil {
			return 0, 0, 0, err
		}

		rebateAmount = memeRebate + contractRebate
		transactionVolume = memeVolume + contractVolume

	case "MEME":
		memeRebate, memeVolume, err := s.getMemeData(ctx, userID, downlineUsers, startTime, endTime)
		if err != nil {
			return 0, 0, 0, err
		}
		rebateAmount = memeRebate
		transactionVolume = memeVolume

	case "CONTRACT":
		contractRebate, contractVolume, err := s.getContractData(ctx, userID, downlineUsers, startTime, endTime)
		if err != nil {
			return 0, 0, 0, err
		}
		rebateAmount = contractRebate
		transactionVolume = contractVolume

	default:
		return 0, 0, 0, fmt.Errorf("unsupported data type: %s", dataType)
	}

	// Get invitation count for the period
	invitationCount, err := s.getInvitationCount(ctx, userID, startTime, endTime)
	if err != nil {
		return 0, 0, 0, err
	}

	return rebateAmount, transactionVolume, invitationCount, nil
}

// getMemeData gets MEME transaction data for a specific period
func (s *DataOverviewService) getMemeData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) (float64, float64, error) {
	// Get MEME transaction volume
	volume, err := s.affiliateRepo.GetVolumeByUserIDsAndPeriod(ctx, downlineUsers, startTime, endTime)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get MEME volume: %w", err)
	}

	// Get MEME rebate amount
	rebate, err := s.commissionRepo.GetRebateAmountByUserIDAndTypeAndPeriod(ctx, userID, "MEME", startTime, endTime)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get MEME rebate: %w", err)
	}

	volumeFloat, _ := volume.Float64()
	rebateFloat, _ := rebate.Float64()

	return rebateFloat, volumeFloat, nil
}

// getContractData gets CONTRACT transaction data for a specific period
func (s *DataOverviewService) getContractData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) (float64, float64, error) {
	// Get CONTRACT transaction volume
	volume, err := s.hyperLiquidRepo.GetVolumeByUserIDsAndPeriod(ctx, downlineUsers, startTime, endTime)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get CONTRACT volume: %w", err)
	}

	// Get CONTRACT rebate amount
	rebate, err := s.commissionRepo.GetRebateAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get CONTRACT rebate: %w", err)
	}

	volumeFloat, _ := volume.Float64()
	rebateFloat, _ := rebate.Float64()

	return rebateFloat, volumeFloat, nil
}

// getInvitationCount gets invitation count for a specific period
func (s *DataOverviewService) getInvitationCount(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) (int, error) {
	count, err := s.userRepo.GetInvitationCountByUserIDAndPeriod(ctx, userID, startTime, endTime)
	if err != nil {
		return 0, fmt.Errorf("failed to get invitation count: %w", err)
	}
	return count, nil
}

// calculateSummary calculates summary statistics from the data
func (s *DataOverviewService) calculateSummary(data []*response.DataOverview) *response.DataOverviewSummary {
	if len(data) == 0 {
		return &response.DataOverviewSummary{}
	}

	var totalRebateAmount, totalTransactionVolume float64
	var totalInvitationCount int
	var peakRebateAmount, peakTransactionVolume float64
	var peakInvitationCount int

	for _, item := range data {
		totalRebateAmount += item.RebateAmount
		totalTransactionVolume += item.TransactionVolume
		totalInvitationCount += item.InvitationCount

		if item.RebateAmount > peakRebateAmount {
			peakRebateAmount = item.RebateAmount
		}
		if item.TransactionVolume > peakTransactionVolume {
			peakTransactionVolume = item.TransactionVolume
		}
		if item.InvitationCount > peakInvitationCount {
			peakInvitationCount = item.InvitationCount
		}
	}

	count := float64(len(data))
	averageRebateAmount := totalRebateAmount / count
	averageTransactionVolume := totalTransactionVolume / count
	averageInvitationCount := float64(totalInvitationCount) / count

	return &response.DataOverviewSummary{
		TotalRebateAmount:        totalRebateAmount,
		TotalTransactionVolume:   totalTransactionVolume,
		TotalInvitationCount:     totalInvitationCount,
		PeakRebateAmount:         peakRebateAmount,
		PeakTransactionVolume:    peakTransactionVolume,
		PeakInvitationCount:      peakInvitationCount,
		AverageRebateAmount:      math.Round(averageRebateAmount*100) / 100,
		AverageTransactionVolume: math.Round(averageTransactionVolume*100) / 100,
		AverageInvitationCount:   math.Round(averageInvitationCount*100) / 100,
	}
}
