package transaction

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// TransactionDataServiceInterface defines the interface for transaction data operations
type TransactionDataServiceInterface interface {
	GetTransactionData(ctx context.Context, userID uuid.UUID, dataType string, timeRange string) ([]*response.TransactionData, error)
}

// TransactionDataService implements transaction data operations
type TransactionDataService struct {
	affiliateRepo   transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo transaction.HyperLiquidTransactionRepositoryInterface
	commissionRepo  transaction.CommissionLedgerRepositoryInterface
	userRepo        transaction.UserRepositoryInterface
}

// NewTransactionDataService creates a new transaction data service
func NewTransactionDataService() TransactionDataServiceInterface {
	return &TransactionDataService{
		affiliateRepo:   transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo: transaction.NewHyperLiquidTransactionRepository(),
		commissionRepo:  transaction.NewCommissionLedgerRepository(),
		userRepo:        transaction.NewUserRepository(),
	}
}

// GetTransactionData retrieves transaction data based on the specified data type and time range
func (s *TransactionDataService) GetTransactionData(ctx context.Context, userID uuid.UUID, dataType string, timeRange string) ([]*response.TransactionData, error) {
	// Get all direct referrals (level 1)
	directReferrals, err := s.userRepo.GetDirectReferrals(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get direct referrals", zap.Error(err))
		return nil, fmt.Errorf("failed to get direct referrals: %w", err)
	}

	// Get all downline users (up to 3 levels)
	allDownlineUsers, err := s.userRepo.GetAllDownlineUsers(ctx, userID, 3)
	if err != nil {
		global.GVA_LOG.Error("Failed to get downline users", zap.Error(err))
		return nil, fmt.Errorf("failed to get downline users: %w", err)
	}

	// Get transacting user count
	transactingUserCount, err := s.getTransactingUserCount(ctx, allDownlineUsers)
	if err != nil {
		global.GVA_LOG.Error("Failed to get transacting user count", zap.Error(err))
		return nil, fmt.Errorf("failed to get transacting user count: %w", err)
	}

	var transactionAmountUsd float64
	var claimedUsd float64
	var pendingClaimUsd float64

	switch dataType {
	case "ALL":
		// Calculate total transaction amount from affiliate transactions (MEME)
		totalMemeAmount, err := s.affiliateRepo.GetTotalVolumeByUserIDs(ctx, allDownlineUsers)
		if err != nil {
			global.GVA_LOG.Error("Failed to get total MEME amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get total MEME amount: %w", err)
		}

		// Calculate total transaction amount from hyperliquid transactions (Contract)
		totalContractAmount, err := s.hyperLiquidRepo.GetTotalVolumeByUserIDs(ctx, allDownlineUsers)
		if err != nil {
			global.GVA_LOG.Error("Failed to get total contract amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get total contract amount: %w", err)
		}

		// Get claimed commission amount
		claimedAmount, err := s.commissionRepo.GetClaimedAmountByUserID(ctx, userID)
		if err != nil {
			global.GVA_LOG.Error("Failed to get claimed amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get claimed amount: %w", err)
		}

		// Get pending claim amount
		pendingClaimAmount, err := s.commissionRepo.GetPendingClaimAmountByUserID(ctx, userID)
		if err != nil {
			global.GVA_LOG.Error("Failed to get pending claim amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get pending claim amount: %w", err)
		}

		totalAmount := totalMemeAmount.Add(totalContractAmount)
		transactionAmountUsd, _ = totalAmount.Float64()
		claimedUsd, _ = claimedAmount.Float64()
		pendingClaimUsd, _ = pendingClaimAmount.Float64()

	case "MEME":
		// Calculate total MEME transaction amount
		totalMemeAmount, err := s.affiliateRepo.GetTotalVolumeByUserIDs(ctx, allDownlineUsers)
		if err != nil {
			global.GVA_LOG.Error("Failed to get total MEME amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get total MEME amount: %w", err)
		}

		// Get claimed MEME commission amount
		claimedMemeAmount, err := s.commissionRepo.GetClaimedAmountByUserIDAndType(ctx, userID, "MEME")
		if err != nil {
			global.GVA_LOG.Error("Failed to get claimed MEME amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get claimed MEME amount: %w", err)
		}

		// Get pending claim MEME amount
		pendingClaimMemeAmount, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndType(ctx, userID, "MEME")
		if err != nil {
			global.GVA_LOG.Error("Failed to get pending claim MEME amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get pending claim MEME amount: %w", err)
		}

		transactionAmountUsd, _ = totalMemeAmount.Float64()
		claimedUsd, _ = claimedMemeAmount.Float64()
		pendingClaimUsd, _ = pendingClaimMemeAmount.Float64()

	case "CONTRACT":
		// Calculate total contract transaction amount
		totalContractAmount, err := s.hyperLiquidRepo.GetTotalVolumeByUserIDs(ctx, allDownlineUsers)
		if err != nil {
			global.GVA_LOG.Error("Failed to get total contract amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get total contract amount: %w", err)
		}

		// Get claimed contract commission amount
		claimedContractAmount, err := s.commissionRepo.GetClaimedAmountByUserIDAndType(ctx, userID, "CONTRACT")
		if err != nil {
			global.GVA_LOG.Error("Failed to get claimed contract amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get claimed contract amount: %w", err)
		}

		// Get pending claim contract amount
		pendingClaimContractAmount, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndType(ctx, userID, "CONTRACT")
		if err != nil {
			global.GVA_LOG.Error("Failed to get pending claim contract amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get pending claim contract amount: %w", err)
		}

		transactionAmountUsd, _ = totalContractAmount.Float64()
		claimedUsd, _ = claimedContractAmount.Float64()
		pendingClaimUsd, _ = pendingClaimContractAmount.Float64()

	default:
		return nil, fmt.Errorf("unsupported data type: %s", dataType)
	}

	// Create transaction data based on time range
	var result []*response.TransactionData

	switch timeRange {
	case "TODAY":
		// Return today's data
		result = append(result, &response.TransactionData{
			TransactionAmountUsd: transactionAmountUsd,
			ClaimedUsd:           claimedUsd,
			PendingClaimUsd:      pendingClaimUsd,
			InvitationCount:      len(directReferrals),
			TransactingUserCount: transactingUserCount,
		})
	case "LAST_30_DAYS":
		// Return last 30 days data
		result = append(result, &response.TransactionData{
			TransactionAmountUsd: transactionAmountUsd,
			ClaimedUsd:           claimedUsd,
			PendingClaimUsd:      pendingClaimUsd,
			InvitationCount:      len(directReferrals),
			TransactingUserCount: transactingUserCount,
		})
	case "LAST_60_DAYS":
		// Return last 60 days data
		result = append(result, &response.TransactionData{
			TransactionAmountUsd: transactionAmountUsd,
			ClaimedUsd:           claimedUsd,
			PendingClaimUsd:      pendingClaimUsd,
			InvitationCount:      len(directReferrals),
			TransactingUserCount: transactingUserCount,
		})
	case "ALL_TIME":
		// Return all time data
		result = append(result, &response.TransactionData{
			TransactionAmountUsd: transactionAmountUsd,
			ClaimedUsd:           claimedUsd,
			PendingClaimUsd:      pendingClaimUsd,
			InvitationCount:      len(directReferrals),
			TransactingUserCount: transactingUserCount,
		})
	default:
		return nil, fmt.Errorf("unsupported time range: %s", timeRange)
	}

	return result, nil
}

// getTransactingUserCount calculates the number of users who have made transactions
func (s *TransactionDataService) getTransactingUserCount(ctx context.Context, userIDs []uuid.UUID) (int, error) {
	if len(userIDs) == 0 {
		return 0, nil
	}

	// Get users who have made MEME transactions
	memeTransactingUsers, err := s.affiliateRepo.GetTransactingUserIDs(ctx, userIDs)
	if err != nil {
		return 0, fmt.Errorf("failed to get MEME transacting users: %w", err)
	}

	// Get users who have made contract transactions
	contractTransactingUsers, err := s.hyperLiquidRepo.GetTransactingUserIDs(ctx, userIDs)
	if err != nil {
		return 0, fmt.Errorf("failed to get contract transacting users: %w", err)
	}

	// Combine and deduplicate user IDs
	allTransactingUsers := make(map[uuid.UUID]bool)
	for _, userID := range memeTransactingUsers {
		allTransactingUsers[userID] = true
	}
	for _, userID := range contractTransactingUsers {
		allTransactingUsers[userID] = true
	}

	return len(allTransactingUsers), nil
}
