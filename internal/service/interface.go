package service

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

type (
	// AgentReferralI defines the interface for agent referral operations
	AgentReferralI interface {
		GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)
		UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string,
			walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string) (*model.User, error)
		CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error
		GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error)
		GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error)
		GetTradingUserCount(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (int, error)
		GetExtendedInvitedUserCount(ctx context.Context, userID uuid.UUID) (int, error)
		GetInvitedAddresses(ctx context.Context, userID uuid.UUID) ([]string, error)
		GetActivityTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error)
		GetContractTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error)
		CreateUserWallet(ctx context.Context, userID uuid.UUID, chain model.ChainType, walletAddress string, walletID *uuid.UUID, walletAccountID *uuid.UUID) (*model.UserWallet, error)
	}

	// InvitationI is an alias for AgentReferralI for backward compatibility
	InvitationI = AgentReferralI

	LevelI interface {
		GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error)
		GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error)
		UpdateLevelCommission(ctx context.Context, levelID uint, directRate, indirectRate, extendedRate, memeFeeRebate float64) (*model.AgentLevel, error)
		GetUserLevelInfo(ctx context.Context, userID uuid.UUID) (*response.UserLevelInfoResponse, error)
	}

	ClaimI interface {
		GetClaimReward(ctx context.Context, userID uuid.UUID) (*response.ClaimRewardResponse, error)
		ClaimMemeReward(ctx context.Context, userID uuid.UUID) error
		ClaimContractReward(ctx context.Context, userID uuid.UUID) error
	}

	HyperLiquidI interface {
		CrawlHyperLiquidBuilderTransaction() error
	}
)
