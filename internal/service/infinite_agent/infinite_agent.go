package infinite_agent

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/infinite_agent"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/infinite"
	"go.uber.org/zap"
)

type InfiniteAgentConfigI interface {
	Create(ctx context.Context, userID uuid.UUID, commissionRateN decimal.Decimal, status string) (*model.InfiniteAgentConfig, error)
	Update(ctx context.Context, id uuid.UUID, userID uuid.UUID, commissionRateN decimal.Decimal, status string) (*model.InfiniteAgentConfig, error)
	GetByID(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) (*model.InfiniteAgentConfig, error)
	GetAll(ctx context.Context) ([]*model.InfiniteAgentConfig, error)
	GetByIDWithUser(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error)
	GetByUserIDWithUser(ctx context.Context, userID uuid.UUID) (*model.InfiniteAgentConfig, error)
	GetAllWithUser(ctx context.Context) ([]*model.InfiniteAgentConfig, error)
}

type InfiniteAgentConfigService struct {
	repo infinite_agent.InfiniteAgentConfigI
}

func NewInfiniteAgentConfigService(repo infinite_agent.InfiniteAgentConfigI) InfiniteAgentConfigI {
	return &InfiniteAgentConfigService{
		repo: repo,
	}
}

func (s *InfiniteAgentConfigService) Create(ctx context.Context, userID uuid.UUID, commissionRateN decimal.Decimal, status string) (*model.InfiniteAgentConfig, error) {
	// Check if infinite agent config already exists for this user
	existingConfig, err := s.repo.GetByUserID(ctx, userID)
	if err == nil && existingConfig != nil {
		return nil, fmt.Errorf("infinite agent config already exists for user %s", userID)
	}

	// Check if ReferralTreeSnapshot table has data from scheduled tasks
	// If no data exists, create task.CreateReferralTreeSnapshots here
	hasSnapshotData, err := s.checkReferralTreeSnapshotData(ctx)
	if err != nil {
		global.GVA_LOG.Warn("Failed to check referral tree snapshot data", zap.Error(err))
		// Don't return error here, as this check is not critical for the main operation
	}

	// If no snapshot data exists, run the scheduled task to create initial snapshots
	if !hasSnapshotData {
		// global.GVA_LOG.Info("No referral tree snapshot data found, creating initial snapshots")
		// err = s.createInitialReferralTreeSnapshots(ctx)
		// if err != nil {
		// 	global.GVA_LOG.Error("Failed to create initial referral tree snapshots", zap.Error(err))
		// 	// Don't return error here, as this is not critical for the main operation
		// }
		return nil, fmt.Errorf("no referral tree snapshot data found, cannot create infinite agent config")
	}

	// Query the node by userID to find the ID, and update the infinite_agent_user_id field of ReferralTreeSnapshot with the id.
	var node model.ReferralTreeNode
	err = global.GVA_DB.WithContext(ctx).Model(&model.ReferralTreeNode{}).
		Where("user_id = ?", userID).
		Select("tree_snapshot_id").
		First(&node).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			global.GVA_LOG.Warn("No referral tree node found for user, cannot create infinite agent config",
				zap.String("user_id", userID.String()))
			return nil, fmt.Errorf("no referral tree node found for user %s", userID)
		} else {
			return nil, fmt.Errorf("failed to find referral tree node for user %s: %w", userID, err)
		}
	}

	var snapshot model.ReferralTreeSnapshot
	err = global.GVA_DB.WithContext(ctx).Model(&model.ReferralTreeSnapshot{}).
		Where("is_valid = true AND id = ?", node.TreeSnapshotID).
		First(&snapshot).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			global.GVA_LOG.Warn("No valid referral tree snapshot found for user, cannot create infinite agent config",
				zap.String("user_id", userID.String()))
			return nil, fmt.Errorf("no valid referral tree snapshot found for user %s", userID)
		} else {
			return nil, fmt.Errorf("failed to find valid referral tree snapshot for user %s: %w", userID, err)
		}
	}
	if snapshot.InfiniteAgentUserID != nil {
		return nil, fmt.Errorf("referral tree snapshot already linked to an infinite agent user %s", snapshot.InfiniteAgentUserID)
	}

	// Create the infinite agent configuration
	config := &model.InfiniteAgentConfig{
		UserID:          userID,
		CommissionRateN: commissionRateN,
		Status:          status,
	}

	err = s.repo.Create(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create infinite agent config: %w", err)
	}

	// Update the infinite_agent_user_id field in the snapshot
	snapshot.InfiniteAgentUserID = &userID
	snapshot.HasInfiniteAgent = true

	err = global.GVA_DB.WithContext(ctx).Updates(&snapshot).Error
	if err != nil {
		return nil, fmt.Errorf("failed to update infinite agent user ID in snapshot for user %s: %w", userID, err)
	}

	// Return the config with user information
	return s.repo.GetByIDWithUser(ctx, config.ID)
}

func (s *InfiniteAgentConfigService) Update(ctx context.Context, id uuid.UUID, userID uuid.UUID, commissionRateN decimal.Decimal, status string) (*model.InfiniteAgentConfig, error) {
	config, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("infinite agent config not found: %w", err)
	}

	// Store the old user ID before updating
	oldUserID := config.UserID

	if userID != uuid.Nil {
		config.UserID = userID
	}
	if !commissionRateN.IsZero() {
		config.CommissionRateN = commissionRateN
	}
	if status != "" {
		config.Status = status
	}

	err = s.repo.Update(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("failed to update infinite agent config: %w", err)
	}

	// Return the updated config with user information
	updatedConfig, err := s.repo.GetByIDWithUser(ctx, config.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated infinite agent config with user: %w", err)
	}

	// Update ReferralTreeSnapshot table if userID has changed
	if userID != uuid.Nil && userID != oldUserID {
		// First, clear the old user ID from any snapshots
		if oldUserID != uuid.Nil {
			err = global.GVA_DB.WithContext(ctx).Model(&model.ReferralTreeSnapshot{}).
				Where("infinite_agent_user_id = ? AND is_valid = true", oldUserID).
				Updates(map[string]interface{}{
					"infinite_agent_user_id": nil,
					"has_infinite_agent":     false,
				}).Error
			if err != nil {
				global.GVA_LOG.Error("Failed to clear old infinite agent user ID from snapshots",
					zap.String("old_user_id", oldUserID.String()),
					zap.Error(err))
				// Don't return error here, as the main config update was successful
			}
		}

		// Find the referral tree node for the new user ID
		var node model.ReferralTreeNode
		err = global.GVA_DB.WithContext(ctx).Model(&model.ReferralTreeNode{}).
			Where("user_id = ?", userID).
			Select("tree_snapshot_id").
			First(&node).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				global.GVA_LOG.Warn("No referral tree node found for new user ID, cannot update snapshot",
					zap.String("user_id", userID.String()))
				// Don't return error here, as the main config update was successful
			} else {
				global.GVA_LOG.Error("Failed to find referral tree node for new user ID",
					zap.String("user_id", userID.String()),
					zap.Error(err))
				// Don't return error here, as the main config update was successful
			}
		} else {
			// Update the snapshot with the new infinite agent user ID
			var snapshot model.ReferralTreeSnapshot
			err = global.GVA_DB.WithContext(ctx).Model(&model.ReferralTreeSnapshot{}).
				Where("is_valid = true AND id = ?", node.TreeSnapshotID).
				First(&snapshot).Error
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					global.GVA_LOG.Warn("No valid referral tree snapshot found for new user ID",
						zap.String("user_id", userID.String()))
				} else {
					global.GVA_LOG.Error("Failed to find valid referral tree snapshot for new user ID",
						zap.String("user_id", userID.String()),
						zap.Error(err))
				}
			} else {
				// Check if snapshot is already linked to another infinite agent
				if snapshot.InfiniteAgentUserID != nil && *snapshot.InfiniteAgentUserID != userID {
					global.GVA_LOG.Warn("Referral tree snapshot already linked to another infinite agent user",
						zap.String("snapshot_id", fmt.Sprintf("%d", snapshot.ID)),
						zap.String("existing_user_id", snapshot.InfiniteAgentUserID.String()),
						zap.String("new_user_id", userID.String()))
				} else {
					// Update the snapshot with the new infinite agent user ID
					snapshot.InfiniteAgentUserID = &userID
					snapshot.HasInfiniteAgent = true

					err = global.GVA_DB.WithContext(ctx).Updates(&snapshot).Error
					if err != nil {
						global.GVA_LOG.Error("Failed to update infinite agent user ID in snapshot",
							zap.String("user_id", userID.String()),
							zap.Error(err))
						// Don't return error here, as the main config update was successful
					} else {
						global.GVA_LOG.Info("Successfully updated infinite agent user ID in referral tree snapshot",
							zap.String("user_id", userID.String()),
							zap.Uint("snapshot_id", snapshot.ID))
					}
				}
			}
		}
	}

	return updatedConfig, nil
}

func (s *InfiniteAgentConfigService) GetByID(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error) {
	config, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get infinite agent config: %w", err)
	}
	return config, nil
}

func (s *InfiniteAgentConfigService) GetByUserID(ctx context.Context, userID uuid.UUID) (*model.InfiniteAgentConfig, error) {
	config, err := s.repo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get infinite agent config by user ID: %w", err)
	}
	return config, nil
}

func (s *InfiniteAgentConfigService) GetAll(ctx context.Context) ([]*model.InfiniteAgentConfig, error) {
	configs, err := s.repo.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get all infinite agent configs: %w", err)
	}
	return configs, nil
}

func (s *InfiniteAgentConfigService) GetByIDWithUser(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error) {
	config, err := s.repo.GetByIDWithUser(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get infinite agent config with user: %w", err)
	}
	return config, nil
}

func (s *InfiniteAgentConfigService) GetByUserIDWithUser(ctx context.Context, userID uuid.UUID) (*model.InfiniteAgentConfig, error) {
	config, err := s.repo.GetByUserIDWithUser(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get infinite agent config by user ID with user: %w", err)
	}
	return config, nil
}

func (s *InfiniteAgentConfigService) GetAllWithUser(ctx context.Context) ([]*model.InfiniteAgentConfig, error) {
	configs, err := s.repo.GetAllWithUser(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get all infinite agent configs with user: %w", err)
	}
	return configs, nil
}

// checkReferralTreeSnapshotData checks if ReferralTreeSnapshot table has any data
func (s *InfiniteAgentConfigService) checkReferralTreeSnapshotData(ctx context.Context) (bool, error) {
	var count int64

	err := global.GVA_DB.WithContext(ctx).Model(&model.ReferralTreeSnapshot{}).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check referral tree snapshot data: %w", err)
	}

	return count > 0, nil
}

// createInitialReferralTreeSnapshots creates initial referral tree snapshots for all root users
func (s *InfiniteAgentConfigService) createInitialReferralTreeSnapshots(ctx context.Context) error {
	// Create referral tree snapshot task instance
	treeSnapshotTask := infinite.NewReferralTreeSnapshotTask()

	// Run the scheduled task to create snapshots for all root users
	global.GVA_LOG.Info("Starting initial referral tree snapshot creation")

	// Execute the task in a goroutine to avoid blocking the main operation
	go func() {
		defer func() {
			if r := recover(); r != nil {
				global.GVA_LOG.Error("Panic in initial referral tree snapshot creation",
					zap.Any("panic", r))
			}
		}()

		treeSnapshotTask.CreateReferralTreeSnapshots()
		global.GVA_LOG.Info("Completed initial referral tree snapshot creation")
	}()

	return nil
}
