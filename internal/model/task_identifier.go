package model

import "fmt"

// TaskIdentifier represents unique identifiers for tasks
type TaskIdentifier string

// Daily Task Identifiers
const (
	TaskIDDailyCheckin           TaskIdentifier = "DAILY_CHECKIN"
	TaskIDMemeTradeDaily         TaskIdentifier = "MEME_TRADE_DAILY"
	TaskIDPerpetualTradeDaily    TaskIdentifier = "PERPETUAL_TRADE_DAILY"
	TaskIDMarketPageView         TaskIdentifier = "MARKET_PAGE_VIEW"
	TaskIDConsecutiveCheckin     TaskIdentifier = "CONSECUTIVE_CHECKIN"
	TaskIDConsecutiveTradingDays TaskIdentifier = "CONSECUTIVE_TRADING_DAYS"
)

// Community Task Identifiers
const (
	TaskIDTwitterFollow  TaskIdentifier = "TWITTER_FOLLOW"
	TaskIDTwitterRetweet TaskIdentifier = "TWITTER_RETWEET"
	TaskIDTwitterLike    TaskIdentifier = "TWITTER_LIKE"
	TaskIDTelegramJoin   TaskIdentifier = "TELEGRAM_JOIN"
	TaskIDInviteFriends  TaskIdentifier = "INVITE_FRIENDS"
	TaskIDShareReferral  TaskIdentifier = "SHARE_REFERRAL"
)

// Trading Task Identifiers
const (
	TaskIDTradingPoints          TaskIdentifier = "TRADING_POINTS"
	TaskIDAccumulatedTrading10K  TaskIdentifier = "ACCUMULATED_TRADING_10K"
	TaskIDAccumulatedTrading50K  TaskIdentifier = "ACCUMULATED_TRADING_50K"
	TaskIDAccumulatedTrading100K TaskIdentifier = "ACCUMULATED_TRADING_100K"
	TaskIDAccumulatedTrading500K TaskIdentifier = "ACCUMULATED_TRADING_500K"
)

// TaskCategoryType defines task categories for better organization
type TaskCategoryType string

const (
	CategoryDaily     TaskCategoryType = "daily"
	CategoryCommunity TaskCategoryType = "community"
	CategoryTrading   TaskCategoryType = "trading"
)

// TaskDefinition contains all information about a task
type TaskDefinition struct {
	Category    TaskCategoryType `json:"category"`
	DisplayName string           `json:"display_name"`
	Description string           `json:"description"`
	Points      int              `json:"points"`
	MaxDaily    *int             `json:"max_daily,omitempty"` // Max completions per day
	RequiresKYC bool             `json:"requires_kyc"`        // Requires KYC verification
	MinTier     *int             `json:"min_tier,omitempty"`  // Minimum user tier required
}

// TaskDefinitionRegistry contains all task definitions with metadata
var TaskDefinitionRegistry = map[TaskIdentifier]TaskDefinition{
	// Daily Tasks
	TaskIDDailyCheckin: {
		Category:    CategoryDaily,
		DisplayName: "Daily Check-in",
		Description: "Check in daily to earn points",
		Points:      5,
		MaxDaily:    func() *int { v := 1; return &v }(),
		RequiresKYC: false,
	},
	TaskIDMemeTradeDaily: {
		Category:    CategoryDaily,
		DisplayName: "Complete one meme trade",
		Description: "Complete one meme trade to earn points",
		Points:      200,
		MaxDaily:    func() *int { v := 1; return &v }(),
		RequiresKYC: true,
	},
	TaskIDPerpetualTradeDaily: {
		Category:    CategoryDaily,
		DisplayName: "Complete one derivatives trade",
		Description: "Complete one derivatives trade to earn points",
		Points:      200,
		MaxDaily:    func() *int { v := 1; return &v }(),
		RequiresKYC: true,
	},
	TaskIDMarketPageView: {
		Category:    CategoryDaily,
		DisplayName: "View market page",
		Description: "View the market page to stay updated",
		Points:      5,
		MaxDaily:    func() *int { v := 1; return &v }(),
		RequiresKYC: false,
	},
	TaskIDConsecutiveCheckin: {
		Category:    CategoryDaily,
		DisplayName: "Consecutive Check-in",
		Description: "Check in consecutively to earn bonus points (3/7/30 days)",
		Points:      0, // Variable points based on streak
		RequiresKYC: false,
	},
	TaskIDConsecutiveTradingDays: {
		Category:    CategoryDaily,
		DisplayName: "Consecutive Trading Days",
		Description: "Trade for consecutive days to earn bonus points (3/7/15/30 days)",
		Points:      0, // Variable points based on streak
		RequiresKYC: true,
	},

	// Community Tasks
	TaskIDTwitterFollow: {
		Category:    CategoryCommunity,
		DisplayName: "Follow on X (Twitter)",
		Description: "Follow our official X account",
		Points:      50,
		RequiresKYC: false,
	},
	TaskIDTwitterRetweet: {
		Category:    CategoryCommunity,
		DisplayName: "Retweet a post",
		Description: "Retweet our latest announcement",
		Points:      25,
		RequiresKYC: false,
	},
	TaskIDTwitterLike: {
		Category:    CategoryCommunity,
		DisplayName: "Like a post",
		Description: "Like our latest post on X",
		Points:      10,
		RequiresKYC: false,
	},
	TaskIDTelegramJoin: {
		Category:    CategoryCommunity,
		DisplayName: "Join Telegram",
		Description: "Join our official Telegram channel",
		Points:      50,
		RequiresKYC: false,
	},
	TaskIDInviteFriends: {
		Category:    CategoryCommunity,
		DisplayName: "Invite friends",
		Description: "Invite friends to join the platform",
		Points:      100,
		RequiresKYC: true,
	},
	TaskIDShareReferral: {
		Category:    CategoryCommunity,
		DisplayName: "Share referral link",
		Description: "Share your referral link on social media",
		Points:      25,
		RequiresKYC: false,
	},

	// Trading Tasks
	TaskIDTradingPoints: {
		Category:    CategoryTrading,
		DisplayName: "Trading Points",
		Description: "Earn points based on trading volume (1-40 points)",
		Points:      0, // Variable points based on volume
		RequiresKYC: true,
	},
	TaskIDAccumulatedTrading10K: {
		Category:    CategoryTrading,
		DisplayName: "Cumulative trading $10,000",
		Description: "Reach $10,000 in cumulative trading volume",
		Points:      300,
		RequiresKYC: true,
	},
	TaskIDAccumulatedTrading50K: {
		Category:    CategoryTrading,
		DisplayName: "Cumulative trading $50,000",
		Description: "Reach $50,000 in cumulative trading volume",
		Points:      1000,
		RequiresKYC: true,
	},
	TaskIDAccumulatedTrading100K: {
		Category:    CategoryTrading,
		DisplayName: "Cumulative trading $100,000",
		Description: "Reach $100,000 in cumulative trading volume",
		Points:      2500,
		RequiresKYC: true,
	},
	TaskIDAccumulatedTrading500K: {
		Category:    CategoryTrading,
		DisplayName: "Cumulative trading $500,000",
		Description: "Reach $500,000 in cumulative trading volume",
		Points:      10000,
		RequiresKYC: true,
	},
}

// GetTaskDefinition returns definition for a task identifier
func GetTaskDefinition(identifier TaskIdentifier) (TaskDefinition, bool) {
	definition, exists := TaskDefinitionRegistry[identifier]
	return definition, exists
}

// GetTasksByCategory returns all tasks for a given category
func GetTasksByCategory(category TaskCategoryType) []TaskIdentifier {
	var tasks []TaskIdentifier
	for identifier, definition := range TaskDefinitionRegistry {
		if definition.Category == category {
			tasks = append(tasks, identifier)
		}
	}
	return tasks
}

// ValidateTaskEligibility checks if user is eligible for a task
func ValidateTaskEligibility(identifier TaskIdentifier, userTier int, isKYCVerified bool) error {
	definition, exists := GetTaskDefinition(identifier)
	if !exists {
		return fmt.Errorf("task not found: %s", identifier)
	}

	if definition.RequiresKYC && !isKYCVerified {
		return fmt.Errorf("task requires KYC verification")
	}

	if definition.MinTier != nil && userTier < *definition.MinTier {
		return fmt.Errorf("task requires minimum tier %d", *definition.MinTier)
	}

	return nil
}

// GetTaskDisplayName returns the display name for a task identifier
func GetTaskDisplayName(identifier TaskIdentifier) string {
	if definition, exists := TaskDefinitionRegistry[identifier]; exists {
		return definition.DisplayName
	}
	return string(identifier)
}

// IsValidTaskIdentifier checks if a task identifier is valid
func IsValidTaskIdentifier(identifier TaskIdentifier) bool {
	_, exists := TaskDefinitionRegistry[identifier]
	return exists
}
