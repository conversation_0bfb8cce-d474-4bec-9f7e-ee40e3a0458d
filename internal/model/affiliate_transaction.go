package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// OrderType represents the type of order
type OrderType string

const (
	Market       OrderType = "Market"
	Limit        OrderType = "Limit"
	TPSL         OrderType = "TPSL"
	TrailingTPSL OrderType = "TrailingTPSL"
)

// TransactionType represents the type of transaction
type TransactionType string

const (
	Buy  TransactionType = "Buy"
	Sell TransactionType = "Sell"
)

// TransactionStatus represents the status of a transaction
type TransactionStatus string

const (
	StatusPending   TransactionStatus = "pending"
	StatusCompleted TransactionStatus = "completed"
	StatusFailed    TransactionStatus = "failed"
	StatusCancelled TransactionStatus = "cancelled"
)

// AffiliateTransaction represents the affiliate_transactions table
// This table stores transaction data and referral relationships for commission calculation
type AffiliateTransaction struct {
	ID        uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	OrderID   uuid.UUID      `gorm:"type:uuid;not null;unique;index" json:"order_id"`
	CreatedAt time.Time      `gorm:"default:CURRENT_TIMESTAMP;index" json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`

	// Transaction Details
	TransactionType TransactionType `gorm:"type:varchar(10);not null;index" json:"transaction_type"`
	Type            OrderType       `gorm:"type:varchar(20);not null" json:"type"`
	ChainID         string          `gorm:"type:varchar(50);not null;index" json:"chain_id"`
	BaseAddress     string          `gorm:"type:varchar(100);not null" json:"base_address"`
	BaseSymbol      string          `gorm:"type:varchar(20);not null;index" json:"base_symbol"`
	QuoteAddress    string          `gorm:"type:varchar(100);not null" json:"quote_address"`
	QuoteSymbol     string          `gorm:"type:varchar(20);not null;index" json:"quote_symbol"`

	// User Information
	UserID      uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	UserAddress string    `gorm:"type:varchar(100);not null;index" json:"user_address"`

	// Transaction Amounts
	BaseAmount  decimal.Decimal `gorm:"type:decimal(36,18);not null" json:"base_amount"`
	QuoteAmount decimal.Decimal `gorm:"type:decimal(36,18);not null" json:"quote_amount"`
	TotalFee    decimal.Decimal `gorm:"type:decimal(36,18);not null" json:"total_fee"`
	Slippage    decimal.Decimal `gorm:"type:decimal(10,6);not null" json:"slippage"`

	// Transaction Status and Hash
	Status     TransactionStatus `gorm:"type:varchar(20);not null;index" json:"status"`
	TxHash     string            `gorm:"type:varchar(100);not null;unique;index" json:"tx_hash"`
	MevProtect bool              `gorm:"default:false" json:"mev_protect"`

	// Referral Information (denormalized for performance)
	ReferrerID    *uuid.UUID `gorm:"type:uuid;index" json:"referrer_id"`
	ReferralDepth int        `gorm:"default:0;index" json:"referral_depth"` // 0 means no referral, 1 means direct referral

	// Commission Tracking
	CommissionRate   decimal.Decimal `gorm:"type:decimal(10,6);default:0" json:"commission_rate"`    // Commission rate applied
	CommissionAmount decimal.Decimal `gorm:"type:decimal(36,18);default:0" json:"commission_amount"` // Commission amount in USD
	CommissionPaid   bool            `gorm:"default:false;index" json:"commission_paid"`             // Whether commission has been paid
	CommissionPaidAt *time.Time      `json:"commission_paid_at"`

	// Volume in USD for easier calculation
	VolumeUSD decimal.Decimal `gorm:"type:decimal(36,18);not null;index" json:"volume_usd"`

	// Relationships
	User     *User `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	Referrer *User `gorm:"foreignKey:ReferrerID;references:ID" json:"referrer,omitempty"`
}

// TableName specifies the table name for AffiliateTransaction
func (AffiliateTransaction) TableName() string {
	return "affiliate_transactions"
}

// BeforeCreate hook to set default values
func (at *AffiliateTransaction) BeforeCreate(tx *gorm.DB) error {
	if at.OrderID == uuid.Nil {
		at.OrderID = uuid.New()
	}
	return nil
}

// IsReferralTransaction checks if this transaction has a referral
func (at *AffiliateTransaction) IsReferralTransaction() bool {
	return at.ReferrerID != nil && at.ReferralDepth > 0
}

// CalculateCommission calculates commission based on volume and rate
func (at *AffiliateTransaction) CalculateCommission() decimal.Decimal {
	if !at.IsReferralTransaction() {
		return decimal.Zero
	}
	return at.VolumeUSD.Mul(at.CommissionRate)
}

// SolPriceSnapshot represents SOL price snapshots for historical tracking
// This table is specialized for SOL only, so no need for symbol, chain_id, address fields
type SolPriceSnapshot struct {
	ID        uint            `gorm:"primaryKey;autoIncrement" json:"id"`
	Price     decimal.Decimal `gorm:"type:decimal(36,18);not null" json:"price"`
	Timestamp time.Time       `gorm:"not null;uniqueIndex" json:"timestamp"` // Unique index to ensure only one record per timestamp
	UpdatedAt time.Time       `json:"updated_at"`
}

// TableName specifies the table name for SolPriceSnapshot
func (SolPriceSnapshot) TableName() string {
	return "sol_price_snapshots"
}
