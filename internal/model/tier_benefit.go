package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// TierBenefit represents the tier_benefits table
type TierBenefit struct {
	ID                  uint            `gorm:"primaryKey;autoIncrement" json:"id"`
	TierLevel           int             `gorm:"not null;unique" json:"tier_level"`
	TierName            string          `gorm:"type:varchar(50);not null" json:"tier_name"`
	MinPoints           int             `gorm:"not null" json:"min_points"`
	CashbackPercentage  decimal.Decimal `gorm:"type:numeric(5,4);not null" json:"cashback_percentage"`
	BenefitsDescription *string         `gorm:"type:text" json:"benefits_description"`
	TierColor           *string         `gorm:"type:varchar(7)" json:"tier_color"` // Hex color code
	TierIcon            *string         `gorm:"type:varchar(255)" json:"tier_icon"`
	IsActive            bool            `gorm:"default:true" json:"is_active"`
	CreatedAt           time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt           time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	Users []UserTierInfo `gorm:"foreignKey:CurrentTier;references:TierLevel" json:"users,omitempty"`
}

// TableName specifies the table name for TierBenefit
func (TierBenefit) TableName() string {
	return "tier_benefits"
}

// GetCashbackRate returns the cashback rate as a float64
func (tb *TierBenefit) GetCashbackRate() float64 {
	rate, _ := tb.CashbackPercentage.Float64()
	return rate
}

// CalculateCashback calculates cashback amount based on trading fees
func (tb *TierBenefit) CalculateCashback(tradingFees decimal.Decimal) decimal.Decimal {
	return tradingFees.Mul(tb.CashbackPercentage)
}

// IsHigherThan checks if this tier is higher than another tier
func (tb *TierBenefit) IsHigherThan(otherTier *TierBenefit) bool {
	return tb.TierLevel > otherTier.TierLevel
}

// GetNextTierRequirement calculates points needed to reach this tier from current points
func (tb *TierBenefit) GetNextTierRequirement(currentPoints int) int {
	remaining := tb.MinPoints - currentPoints
	if remaining < 0 {
		return 0
	}
	return remaining
}
