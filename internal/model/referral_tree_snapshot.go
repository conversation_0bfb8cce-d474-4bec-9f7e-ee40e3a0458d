package model

import (
	"time"

	"github.com/google/uuid"
)

// ReferralTreeSnapshot represents the referral_tree_snapshots table
// Used to record complete snapshots of referral trees, including tree structure and statistics
type ReferralTreeSnapshot struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Tree basic information
	RootUserID   uuid.UUID `gorm:"type:uuid;not null;index" json:"root_user_id"` // Root user ID (top user of the tree)
	SnapshotDate time.Time `gorm:"not null;index" json:"snapshot_date"`          // Snapshot date

	// Tree structure information
	TotalNodes  int `gorm:"default:0" json:"total_nodes"`  // Total nodes in the tree
	MaxDepth    int `gorm:"default:0" json:"max_depth"`    // Maximum depth of the tree
	DirectCount int `gorm:"default:0" json:"direct_count"` // Direct referral count (L1)

	// Statistics
	ActiveUsers  int `gorm:"default:0" json:"active_users"`  // Number of active users
	TradingUsers int `gorm:"default:0" json:"trading_users"` // Number of trading users

	// Infinite agent information
	InfiniteAgentUserID *uuid.UUID `gorm:"type:uuid;index" json:"infinite_agent_user_id"` // Infinite agent user ID
	HasInfiniteAgent    bool       `gorm:"default:false" json:"has_infinite_agent"`       // Whether contains infinite agent

	// Remarks
	Description string `gorm:"type:text" json:"description"` // Snapshot description
	IsValid     bool   `gorm:"default:true" json:"is_valid"` // Whether snapshot is valid

	// Relationships
	RootUser            User                 `gorm:"foreignKey:RootUserID;references:ID" json:"root_user"`
	InfiniteAgentUser   *User                `gorm:"foreignKey:InfiniteAgentUserID;references:ID" json:"infinite_agent_user"`
	InfiniteAgentConfig *InfiniteAgentConfig `gorm:"foreignKey:InfiniteAgentUserID;references:UserID" json:"infinite_agent_config"`
}

// TableName specifies the table name for ReferralTreeSnapshot
func (ReferralTreeSnapshot) TableName() string {
	return "referral_tree_snapshots"
}

// ReferralTreeNode represents the referral_tree_nodes table
// Used to record detailed information of each node in the referral tree
type ReferralTreeNode struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Association to tree snapshot
	TreeSnapshotID uint `gorm:"not null;index" json:"tree_snapshot_id"`

	// Node information
	UserID       uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"` // User ID
	ParentUserID *uuid.UUID `gorm:"type:uuid;index" json:"parent_user_id"`   // Parent user ID
	ReferrerID   *uuid.UUID `gorm:"type:uuid;index" json:"referrer_id"`      // Referrer ID

	// Tree structure information
	Depth    int `gorm:"default:0" json:"depth"`    // Depth in the tree
	Level    int `gorm:"default:0" json:"level"`    // Level (L1, L2, L3...)
	Position int `gorm:"default:0" json:"position"` // Position among siblings

	// User status information
	IsActive     bool `gorm:"default:true" json:"is_active"`   // Whether active
	IsTrading    bool `gorm:"default:false" json:"is_trading"` // Whether trading user
	AgentLevelID uint `gorm:"default:1" json:"agent_level_id"` // Agent level

	// Relationships
	TreeSnapshot ReferralTreeSnapshot `gorm:"foreignKey:TreeSnapshotID;references:ID" json:"tree_snapshot"`
	User         User                 `gorm:"foreignKey:UserID;references:ID" json:"user"`
	ParentUser   *User                `gorm:"foreignKey:ParentUserID;references:ID" json:"parent_user"`
	Referrer     *User                `gorm:"foreignKey:ReferrerID;references:ID" json:"referrer"`
	AgentLevel   AgentLevel           `gorm:"foreignKey:AgentLevelID;references:ID" json:"agent_level"`
}

// TableName specifies the table name for ReferralTreeNode
func (ReferralTreeNode) TableName() string {
	return "referral_tree_nodes"
}
