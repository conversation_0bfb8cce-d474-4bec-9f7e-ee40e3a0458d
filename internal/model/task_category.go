package model

import (
	"time"
)

// TaskCategory represents the task_categories table
type TaskCategory struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	Name        string    `gorm:"type:varchar(50);not null;unique" json:"name"`
	DisplayN<PERSON> string    `gorm:"type:varchar(100);not null" json:"display_name"`
	Description *string   `gorm:"type:text" json:"description"`
	Icon        *string   `gorm:"type:varchar(255)" json:"icon"`
	SortOrder   int       `gorm:"default:0" json:"sort_order"`
	IsActive    bool      `gorm:"default:true" json:"is_active"`
	CreatedAt   time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	Tasks []ActivityTask `gorm:"foreignKey:CategoryID;references:ID" json:"tasks,omitempty"`
}

// TableName specifies the table name for TaskCategory
func (TaskCategory) TableName() string {
	return "task_categories"
}
