package graphql

import (
	"context"
	"errors"

	"github.com/99designs/gqlgen/graphql"
	"github.com/gin-gonic/gin"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/middlewares"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
)

// AdminAuthDirective validates admin API key authentication for admin GraphQL
func AdminAuthDirective(ctx context.Context, obj interface{}, next graphql.Resolver) (res interface{}, err error) {
	// Check if admin context is set (from API key middleware)
	isAdmin := ctx.Value("isAdmin")
	if isAdmin != nil && isAdmin.(bool) {
		return next(ctx)
	}

	// Fallback: check for API key in gin context
	ginCtx := ctx.Value(middlewares.GinContextKey)
	if ginCtx != nil {
		if ginContext, ok := ginCtx.(*gin.Context); ok {
			apiKey := ginContext.GetHeader("x-api-key")
			if apiKey == "" {
				apiKey = ginContext.GetHeader("X-API-Key")
			}

			// Validate API key
			if apiKey != "" && apiKey == global.GVA_CONFIG.Admin.InternalAPIKey {
				return next(ctx)
			}
		}
	}

	return nil, errors.New("admin access required: invalid or missing API key")
}
