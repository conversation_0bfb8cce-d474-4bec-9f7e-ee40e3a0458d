// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package gql_model

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
	"time"
)

type ActivityTask struct {
	ID                 string          `json:"id"`
	CategoryID         string          `json:"categoryId"`
	Category           *TaskCategory   `json:"category"`
	Name               string          `json:"name"`
	Description        *string         `json:"description,omitempty"`
	TaskType           TaskType        `json:"taskType"`
	Frequency          TaskFrequency   `json:"frequency"`
	TaskIdentifier     *TaskIdentifier `json:"taskIdentifier,omitempty"`
	Points             int             `json:"points"`
	MaxCompletions     *int            `json:"maxCompletions,omitempty"`
	ResetPeriod        *string         `json:"resetPeriod,omitempty"`
	Conditions         *string         `json:"conditions,omitempty"`
	ActionTarget       *string         `json:"actionTarget,omitempty"`
	VerificationMethod *string         `json:"verificationMethod,omitempty"`
	ExternalLink       *string         `json:"externalLink,omitempty"`
	StartDate          *time.Time      `json:"startDate,omitempty"`
	EndDate            *time.Time      `json:"endDate,omitempty"`
	SortOrder          int             `json:"sortOrder"`
	IsActive           bool            `json:"isActive"`
	CreatedAt          time.Time       `json:"createdAt"`
	UpdatedAt          time.Time       `json:"updatedAt"`
}

type AdminStatsInput struct {
	StartDate time.Time `json:"startDate"`
	EndDate   time.Time `json:"endDate"`
}

type AdminTaskCompletionStats struct {
	TaskCompletions []*TaskCompletionStat `json:"taskCompletions"`
	StartDate       time.Time             `json:"startDate"`
	EndDate         time.Time             `json:"endDate"`
	TotalTasks      int                   `json:"totalTasks"`
}

type AdminTaskCompletionStatsResponse struct {
	Success bool                      `json:"success"`
	Message string                    `json:"message"`
	Data    *AdminTaskCompletionStats `json:"data,omitempty"`
}

type AdminTierDistributionResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    []*TierDistributionStat `json:"data"`
}

type AdminUserActivityStats struct {
	DailyCompletions []*DailyCompletionStat `json:"dailyCompletions"`
	StartDate        time.Time              `json:"startDate"`
	EndDate          time.Time              `json:"endDate"`
}

type AdminUserActivityStatsResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    *AdminUserActivityStats `json:"data,omitempty"`
}

type CreateTaskCategoryInput struct {
	Name        string  `json:"name"`
	DisplayName string  `json:"displayName"`
	Description *string `json:"description,omitempty"`
}

type CreateTaskInput struct {
	CategoryID         string          `json:"categoryId"`
	Name               string          `json:"name"`
	Description        *string         `json:"description,omitempty"`
	TaskType           TaskType        `json:"taskType"`
	Frequency          TaskFrequency   `json:"frequency"`
	TaskIdentifier     *TaskIdentifier `json:"taskIdentifier,omitempty"`
	Points             int             `json:"points"`
	MaxCompletions     *int            `json:"maxCompletions,omitempty"`
	ResetPeriod        *string         `json:"resetPeriod,omitempty"`
	Conditions         *string         `json:"conditions,omitempty"`
	ActionTarget       *string         `json:"actionTarget,omitempty"`
	VerificationMethod *string         `json:"verificationMethod,omitempty"`
	ExternalLink       *string         `json:"externalLink,omitempty"`
	StartDate          *time.Time      `json:"startDate,omitempty"`
	EndDate            *time.Time      `json:"endDate,omitempty"`
	SortOrder          *int            `json:"sortOrder,omitempty"`
}

type CreateTierBenefitInput struct {
	TierLevel           int     `json:"tierLevel"`
	TierName            string  `json:"tierName"`
	MinPoints           int     `json:"minPoints"`
	CashbackPercentage  float64 `json:"cashbackPercentage"`
	BenefitsDescription *string `json:"benefitsDescription,omitempty"`
	TierColor           *string `json:"tierColor,omitempty"`
	TierIcon            *string `json:"tierIcon,omitempty"`
}

type DailyCompletionStat struct {
	Date            string `json:"date"`
	CompletionCount int    `json:"completionCount"`
}

type Mutation struct {
}

type Query struct {
}

type TaskCategory struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	DisplayName string    `json:"displayName"`
	Description *string   `json:"description,omitempty"`
	IsActive    bool      `json:"isActive"`
	SortOrder   int       `json:"sortOrder"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

type TaskCompletionStat struct {
	TaskName        string `json:"taskName"`
	CompletionCount int    `json:"completionCount"`
}

type TierBenefit struct {
	ID                  string    `json:"id"`
	TierLevel           int       `json:"tierLevel"`
	TierName            string    `json:"tierName"`
	MinPoints           int       `json:"minPoints"`
	CashbackPercentage  float64   `json:"cashbackPercentage"`
	BenefitsDescription *string   `json:"benefitsDescription,omitempty"`
	TierColor           *string   `json:"tierColor,omitempty"`
	TierIcon            *string   `json:"tierIcon,omitempty"`
	IsActive            bool      `json:"isActive"`
	CreatedAt           time.Time `json:"createdAt"`
	UpdatedAt           time.Time `json:"updatedAt"`
}

type TierBenefitResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message"`
	Data    *TierBenefit `json:"data,omitempty"`
}

type TierDistributionStat struct {
	TierLevel int `json:"tierLevel"`
	UserCount int `json:"userCount"`
}

type UpdateTaskCategoryInput struct {
	ID          string  `json:"id"`
	Name        *string `json:"name,omitempty"`
	DisplayName *string `json:"displayName,omitempty"`
	Description *string `json:"description,omitempty"`
	IsActive    *bool   `json:"isActive,omitempty"`
	SortOrder   *int    `json:"sortOrder,omitempty"`
}

type UpdateTaskInput struct {
	ID                 string          `json:"id"`
	CategoryID         *string         `json:"categoryId,omitempty"`
	Name               *string         `json:"name,omitempty"`
	Description        *string         `json:"description,omitempty"`
	TaskType           *TaskType       `json:"taskType,omitempty"`
	Frequency          *TaskFrequency  `json:"frequency,omitempty"`
	TaskIdentifier     *TaskIdentifier `json:"taskIdentifier,omitempty"`
	Points             *int            `json:"points,omitempty"`
	MaxCompletions     *int            `json:"maxCompletions,omitempty"`
	ResetPeriod        *string         `json:"resetPeriod,omitempty"`
	Conditions         *string         `json:"conditions,omitempty"`
	ActionTarget       *string         `json:"actionTarget,omitempty"`
	VerificationMethod *string         `json:"verificationMethod,omitempty"`
	ExternalLink       *string         `json:"externalLink,omitempty"`
	StartDate          *time.Time      `json:"startDate,omitempty"`
	EndDate            *time.Time      `json:"endDate,omitempty"`
	SortOrder          *int            `json:"sortOrder,omitempty"`
	IsActive           *bool           `json:"isActive,omitempty"`
}

type UpdateTierBenefitInput struct {
	ID                  string   `json:"id"`
	TierLevel           *int     `json:"tierLevel,omitempty"`
	TierName            *string  `json:"tierName,omitempty"`
	MinPoints           *int     `json:"minPoints,omitempty"`
	CashbackPercentage  *float64 `json:"cashbackPercentage,omitempty"`
	BenefitsDescription *string  `json:"benefitsDescription,omitempty"`
	TierColor           *string  `json:"tierColor,omitempty"`
	TierIcon            *string  `json:"tierIcon,omitempty"`
	IsActive            *bool    `json:"isActive,omitempty"`
}

type UserTierInfo struct {
	UserID               string       `json:"userId"`
	Email                *string      `json:"email,omitempty"`
	CurrentTier          *TierBenefit `json:"currentTier,omitempty"`
	TotalPoints          int          `json:"totalPoints"`
	AvailableCashback    float64      `json:"availableCashback"`
	TotalCashbackClaimed float64      `json:"totalCashbackClaimed"`
	NextTier             *TierBenefit `json:"nextTier,omitempty"`
	PointsToNextTier     *int         `json:"pointsToNextTier,omitempty"`
	CreatedAt            time.Time    `json:"createdAt"`
	LastActivityAt       *time.Time   `json:"lastActivityAt,omitempty"`
}

type TaskFrequency string

const (
	TaskFrequencyOnce      TaskFrequency = "ONCE"
	TaskFrequencyDaily     TaskFrequency = "DAILY"
	TaskFrequencyWeekly    TaskFrequency = "WEEKLY"
	TaskFrequencyMonthly   TaskFrequency = "MONTHLY"
	TaskFrequencyUnlimited TaskFrequency = "UNLIMITED"
)

var AllTaskFrequency = []TaskFrequency{
	TaskFrequencyOnce,
	TaskFrequencyDaily,
	TaskFrequencyWeekly,
	TaskFrequencyMonthly,
	TaskFrequencyUnlimited,
}

func (e TaskFrequency) IsValid() bool {
	switch e {
	case TaskFrequencyOnce, TaskFrequencyDaily, TaskFrequencyWeekly, TaskFrequencyMonthly, TaskFrequencyUnlimited:
		return true
	}
	return false
}

func (e TaskFrequency) String() string {
	return string(e)
}

func (e *TaskFrequency) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskFrequency(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskFrequency", str)
	}
	return nil
}

func (e TaskFrequency) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskFrequency) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskFrequency) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskIdentifier string

const (
	TaskIdentifierFirstTrade    TaskIdentifier = "FIRST_TRADE"
	TaskIdentifierDailyTrade    TaskIdentifier = "DAILY_TRADE"
	TaskIdentifierWeeklyVolume  TaskIdentifier = "WEEKLY_VOLUME"
	TaskIdentifierMonthlyVolume TaskIdentifier = "MONTHLY_VOLUME"
	TaskIdentifierInviteFriend  TaskIdentifier = "INVITE_FRIEND"
	TaskIdentifierCompleteKyc   TaskIdentifier = "COMPLETE_KYC"
	TaskIdentifierFirstDeposit  TaskIdentifier = "FIRST_DEPOSIT"
	TaskIdentifierSocialShare   TaskIdentifier = "SOCIAL_SHARE"
	TaskIdentifierCustomAction  TaskIdentifier = "CUSTOM_ACTION"
)

var AllTaskIdentifier = []TaskIdentifier{
	TaskIdentifierFirstTrade,
	TaskIdentifierDailyTrade,
	TaskIdentifierWeeklyVolume,
	TaskIdentifierMonthlyVolume,
	TaskIdentifierInviteFriend,
	TaskIdentifierCompleteKyc,
	TaskIdentifierFirstDeposit,
	TaskIdentifierSocialShare,
	TaskIdentifierCustomAction,
}

func (e TaskIdentifier) IsValid() bool {
	switch e {
	case TaskIdentifierFirstTrade, TaskIdentifierDailyTrade, TaskIdentifierWeeklyVolume, TaskIdentifierMonthlyVolume, TaskIdentifierInviteFriend, TaskIdentifierCompleteKyc, TaskIdentifierFirstDeposit, TaskIdentifierSocialShare, TaskIdentifierCustomAction:
		return true
	}
	return false
}

func (e TaskIdentifier) String() string {
	return string(e)
}

func (e *TaskIdentifier) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskIdentifier(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskIdentifier", str)
	}
	return nil
}

func (e TaskIdentifier) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskIdentifier) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskIdentifier) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskType string

const (
	TaskTypeTrading    TaskType = "TRADING"
	TaskTypeSocial     TaskType = "SOCIAL"
	TaskTypeReferral   TaskType = "REFERRAL"
	TaskTypeDeposit    TaskType = "DEPOSIT"
	TaskTypeWithdrawal TaskType = "WITHDRAWAL"
	TaskTypeKyc        TaskType = "KYC"
	TaskTypeCustom     TaskType = "CUSTOM"
)

var AllTaskType = []TaskType{
	TaskTypeTrading,
	TaskTypeSocial,
	TaskTypeReferral,
	TaskTypeDeposit,
	TaskTypeWithdrawal,
	TaskTypeKyc,
	TaskTypeCustom,
}

func (e TaskType) IsValid() bool {
	switch e {
	case TaskTypeTrading, TaskTypeSocial, TaskTypeReferral, TaskTypeDeposit, TaskTypeWithdrawal, TaskTypeKyc, TaskTypeCustom:
		return true
	}
	return false
}

func (e TaskType) String() string {
	return string(e)
}

func (e *TaskType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskType", str)
	}
	return nil
}

func (e TaskType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
