package middlewares

import (
	"context"
	"strings"

	"github.com/gin-gonic/gin"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
)

// ApiKeyAuth middleware for validating API key authentication
func ApiKeyAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		apiKey := ctx.GetHeader("x-api-key")
		if apiKey == "" {
			// Try alternative header names
			apiKey = ctx.GetHeader("X-API-Key")
			if apiKey == "" {
				apiKey = ctx.GetHeader("Authorization")
				if strings.HasPrefix(apiKey, "Bearer ") {
					apiKey = strings.TrimPrefix(apiKey, "Bearer ")
				}
			}
		}

		// Validate API key
		if apiKey == "" || apiKey != global.GVA_CONFIG.Admin.InternalAPIKey {
			ctx.JSON(401, gin.H{
				"error": "unauthorized",
				"message": "invalid or missing API key",
			})
			ctx.Abort()
			return
		}

		// Set admin context for GraphQL resolvers
		wrappedCtx := context.WithValue(ctx.Request.Context(), "isAdmin", true)
		wrappedCtx = context.WithValue(wrappedCtx, "adminApiKey", apiKey)
		ctx.Request = ctx.Request.WithContext(wrappedCtx)
		ctx.Next()
	}
}

// ValidateApiKey validates API key without middleware context
func ValidateApiKey(apiKey string) bool {
	return apiKey != "" && apiKey == global.GVA_CONFIG.Admin.InternalAPIKey
}
