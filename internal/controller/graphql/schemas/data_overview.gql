# Data Overview schema

type DataOverview {
  rebateAmount: Float!
  transactionVolume: Float!
  invitationCount: Int!
  timestamp: Time!
  period: String!
}

enum DataOverviewType {
  ALL
  MEME
  CONTRACT
}

enum DataOverviewTimeRange {
  ONE_DAY
  THIRTY_DAYS
  SIXTY_DAYS
  ALL_TIME
}

input DataOverviewInput {
  dataType: DataOverviewType!
  timeRange: DataOverviewTimeRange!
}

type DataOverviewResponse {
  data: [DataOverview!]!
  success: Boolean!
  message: String
}

type DataOverviewSummary {
  totalRebateAmount: Float!
  totalTransactionVolume: Float!
  totalInvitationCount: Int!
  peakRebateAmount: Float!
  peakTransactionVolume: Float!
  peakInvitationCount: Int!
  averageRebateAmount: Float!
  averageTransactionVolume: Float!
  averageInvitationCount: Float!
}

type DataOverviewWithSummary {
  data: [DataOverview!]!
  summary: DataOverviewSummary!
  success: Boolean!
  message: String
}
