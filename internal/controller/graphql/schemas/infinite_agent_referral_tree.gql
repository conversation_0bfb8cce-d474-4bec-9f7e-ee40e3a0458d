# Infinite Agent Referral Tree and Node management schema

type InfiniteAgentReferralTree {
  id: ID!
  createdAt: Time!
  infiniteAgentUserId: ID!
  commissionRateN: Float!
  rootUserId: ID!
  snapshotDate: Time!
  totalNodes: Int!
  maxDepth: Int!
  directCount: Int!
  activeUsers: Int!
  tradingUsers: Int!
  totalCommissionEarned: Float!
  totalVolumeUsd: Float!
  status: String!
  description: String
  infiniteAgentUser: User!
  infiniteAgentConfig: InfiniteAgentConfig!
  rootUser: User!
  treeNodes: [InfiniteAgentTreeNode!]!
}

type InfiniteAgentTreeNode {
  id: ID!
  createdAt: Time!
  treeID: ID!
  userID: ID!
  parentUserID: ID
  referrerID: ID
  depth: Int!
  level: Int!
  position: Int!
  isActive: Boolean!
  isTrading: Boolean!
  agentLevelID: Int!
  commissionEarned: Float!
  volumeUsd: Float!
  feeVolumeUsd: Float!
  tree: InfiniteAgentReferralTree!
  user: User!
  parentUser: User
  referrer: User
  agentLevel: AgentLevel!
}

type InfiniteAgentReferralTreesResponse {
  infiniteAgentReferralTrees: [InfiniteAgentReferralTree!]!
  success: Boolean!
  message: String!
}

type InfiniteAgentReferralTreeResponse {
  infiniteAgentReferralTree: InfiniteAgentReferralTree
  infiniteAgentTreeNodes: [InfiniteAgentTreeNode!]!
  success: Boolean!
  message: String!
}
