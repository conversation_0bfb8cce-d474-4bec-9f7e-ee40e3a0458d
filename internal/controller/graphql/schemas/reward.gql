type RewardData {
  accumulatedRewards: Float!
  usdcRebate: Float!
  solRebate: Float!
  invitationTransactionRank: Int!
  invitationRevenueRank: Int!
  progressPrompt: String!
}

type InvitationRecord {
  acceptedInvitationAddress: String!
  addressTransactionVolume: Float!
  subordinateInvitedAddressCount: Int!
  addressHandlingFee: Float!
}

type RewardDataResponse {
  success: Boolean!
  message: String!
  data: RewardData
}

type InvitationRecordResponse {
  success: Boolean!
  message: String!
  data: [InvitationRecord!]!
}

