package resolvers

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/transaction"
)

type TransactionResolver struct {
	s transaction.TransactionDataServiceInterface
}

func NewTransactionResolver() *TransactionResolver {
	return &TransactionResolver{
		s: transaction.NewTransactionDataService(),
	}
}

// TransactionData handles the transaction data query
func (t *TransactionResolver) TransactionData(ctx context.Context, input gql_model.TransactionDataInput) (*gql_model.TransactionDataResponse, error) {
	// Get user ID from context
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return nil, fmt.Errorf("user ID not found in context")
	}

	// Convert GraphQL enum to string
	dataType := string(input.DataType)
	timeRange := string(input.TimeRange)

	// Get transaction data based on data type and time range
	transactionDataList, err := t.s.GetTransactionData(ctx, userID, dataType, timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction data: %w", err)
	}

	// Convert to GraphQL response
	response := &gql_model.TransactionDataResponse{
		TransactionData: DTOTransactionDataListToGQL(transactionDataList),
		Success:         true,
	}

	return response, nil
}

// DTOTransactionDataListToGQL converts DTO list to GraphQL model list
func DTOTransactionDataListToGQL(dataList []*response.TransactionData) []*gql_model.TransactionData {
	if dataList == nil {
		return nil
	}

	var result []*gql_model.TransactionData
	for _, data := range dataList {
		if data != nil {
			result = append(result, &gql_model.TransactionData{
				TransactionAmountUsd: data.TransactionAmountUsd,
				ClaimedUsd:           data.ClaimedUsd,
				PendingClaimUsd:      data.PendingClaimUsd,
				InvitationCount:      data.InvitationCount,
				TransactingUserCount: data.TransactingUserCount,
			})
		}
	}

	return result
}
