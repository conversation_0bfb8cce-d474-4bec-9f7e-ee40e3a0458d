package resolvers

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type InfiniteAgentReferralTreeResolver struct{}

func NewInfiniteAgentReferralTreeResolver() *InfiniteAgentReferralTreeResolver {
	return &InfiniteAgentReferralTreeResolver{}
}

// InfiniteAgentReferralTrees is the resolver for the infiniteAgentReferralTrees field.
func (r *InfiniteAgentReferralTreeResolver) InfiniteAgentReferralTrees(ctx context.Context) (*gql_model.InfiniteAgentReferralTreesResponse, error) {
	var trees []model.InfiniteAgentReferralTree

	err := global.GVA_DB.WithContext(ctx).
		Preload("InfiniteAgentUser").
		Preload("InfiniteAgentConfig").
		Preload("RootUser").
		Preload("TreeNodes").
		Where("status = ?", "ACTIVE").
		Find(&trees).Error

	if err != nil {
		global.GVA_LOG.Error("Failed to get infinite agent referral trees", zap.Error(err))
		return &gql_model.InfiniteAgentReferralTreesResponse{
			InfiniteAgentReferralTrees: []*gql_model.InfiniteAgentReferralTree{},
			Success:                    false,
			Message:                    fmt.Sprintf("Failed to get infinite agent referral trees: %v", err),
		}, nil
	}

	var gqlTrees []*gql_model.InfiniteAgentReferralTree
	for _, tree := range trees {
		gqlTree := ModelInfiniteAgentReferralTreeToGQL(&tree)
		gqlTrees = append(gqlTrees, gqlTree)
	}

	return &gql_model.InfiniteAgentReferralTreesResponse{
		InfiniteAgentReferralTrees: gqlTrees,
		Success:                    true,
		Message:                    "Successfully retrieved infinite agent referral trees",
	}, nil
}

// InfiniteAgentReferralTree is the resolver for the infiniteAgentReferralTree field.
func (r *InfiniteAgentReferralTreeResolver) InfiniteAgentReferralTree(ctx context.Context, id string) (*gql_model.InfiniteAgentReferralTreeResponse, error) {
	var tree model.InfiniteAgentReferralTree
	err := global.GVA_DB.WithContext(ctx).
		Preload("InfiniteAgentUser").
		Preload("InfiniteAgentConfig").
		Preload("RootUser").
		Preload("TreeNodes").
		Where("id = ? AND status = ?", id, "ACTIVE").
		First(&tree).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &gql_model.InfiniteAgentReferralTreeResponse{
				InfiniteAgentReferralTree: nil,
				InfiniteAgentTreeNodes:    []*gql_model.InfiniteAgentTreeNode{},
				Success:                   false,
				Message:                   "Infinite agent referral tree not found",
			}, nil
		}
		global.GVA_LOG.Error("Failed to get infinite agent referral tree", zap.String("id", id), zap.Error(err))
		return &gql_model.InfiniteAgentReferralTreeResponse{
			InfiniteAgentReferralTree: nil,
			InfiniteAgentTreeNodes:    []*gql_model.InfiniteAgentTreeNode{},
			Success:                   false,
			Message:                   fmt.Sprintf("Failed to get infinite agent referral tree: %v", err),
		}, nil
	}

	// Get tree nodes for this tree
	var nodes []model.InfiniteAgentTreeNode
	err = global.GVA_DB.WithContext(ctx).
		Preload("Tree").
		Preload("User").
		Preload("ParentUser").
		Preload("Referrer").
		Preload("AgentLevel").
		Where("tree_id = ?", tree.ID).
		Order("level, position").
		Find(&nodes).Error

	if err != nil {
		global.GVA_LOG.Error("Failed to get infinite agent tree nodes", zap.Uint("tree_id", tree.ID), zap.Error(err))
		return &gql_model.InfiniteAgentReferralTreeResponse{
			InfiniteAgentReferralTree: ModelInfiniteAgentReferralTreeToGQL(&tree),
			InfiniteAgentTreeNodes:    []*gql_model.InfiniteAgentTreeNode{},
			Success:                   false,
			Message:                   fmt.Sprintf("Failed to get infinite agent tree nodes: %v", err),
		}, nil
	}

	var gqlNodes []*gql_model.InfiniteAgentTreeNode
	for _, node := range nodes {
		gqlNode := ModelInfiniteAgentTreeNodeToGQL(&node)
		gqlNodes = append(gqlNodes, gqlNode)
	}

	return &gql_model.InfiniteAgentReferralTreeResponse{
		InfiniteAgentReferralTree: ModelInfiniteAgentReferralTreeToGQL(&tree),
		InfiniteAgentTreeNodes:    gqlNodes,
		Success:                   true,
		Message:                   "Successfully retrieved infinite agent referral tree and nodes",
	}, nil
}

// ModelInfiniteAgentReferralTreeToGQL converts model.InfiniteAgentReferralTree to gql_model.InfiniteAgentReferralTree
func ModelInfiniteAgentReferralTreeToGQL(tree *model.InfiniteAgentReferralTree) *gql_model.InfiniteAgentReferralTree {
	if tree == nil {
		return nil
	}

	commissionRateN, _ := tree.CommissionRateN.Float64()
	totalCommissionEarned, _ := tree.TotalCommissionEarned.Float64()
	totalVolumeUsd, _ := tree.TotalVolumeUSD.Float64()

	gqlTree := &gql_model.InfiniteAgentReferralTree{
		ID:                    fmt.Sprintf("%d", tree.ID),
		CreatedAt:             tree.CreatedAt,
		InfiniteAgentUserID:   tree.InfiniteAgentUserID.String(),
		CommissionRateN:       commissionRateN,
		RootUserID:            tree.RootUserID.String(),
		SnapshotDate:          tree.SnapshotDate,
		TotalNodes:            tree.TotalNodes,
		MaxDepth:              tree.MaxDepth,
		DirectCount:           tree.DirectCount,
		ActiveUsers:           tree.ActiveUsers,
		TradingUsers:          tree.TradingUsers,
		TotalCommissionEarned: totalCommissionEarned,
		TotalVolumeUsd:        totalVolumeUsd,
		Status:                tree.Status,
		Description:           &tree.Description,
		InfiniteAgentUser:     utils.Translate[gql_model.User](&tree.InfiniteAgentUser),
		InfiniteAgentConfig:   utils.Translate[gql_model.InfiniteAgentConfig](&tree.InfiniteAgentConfig),
		RootUser:              utils.Translate[gql_model.User](&tree.RootUser),
	}

	// Convert tree nodes
	var gqlTreeNodes []*gql_model.InfiniteAgentTreeNode
	for _, node := range tree.TreeNodes {
		gqlNode := ModelInfiniteAgentTreeNodeToGQL(&node)
		gqlTreeNodes = append(gqlTreeNodes, gqlNode)
	}
	gqlTree.TreeNodes = gqlTreeNodes

	return gqlTree
}

// ModelInfiniteAgentTreeNodeToGQL converts model.InfiniteAgentTreeNode to gql_model.InfiniteAgentTreeNode
func ModelInfiniteAgentTreeNodeToGQL(node *model.InfiniteAgentTreeNode) *gql_model.InfiniteAgentTreeNode {
	if node == nil {
		return nil
	}

	commissionEarned, _ := node.CommissionEarned.Float64()
	volumeUsd, _ := node.VolumeUSD.Float64()
	feeVolumeUsd, _ := node.FeeVolumeUSD.Float64()

	gqlNode := &gql_model.InfiniteAgentTreeNode{
		ID:               fmt.Sprintf("%d", node.ID),
		CreatedAt:        node.CreatedAt,
		TreeID:           fmt.Sprintf("%d", node.TreeID),
		UserID:           node.UserID.String(),
		Depth:            node.Depth,
		Level:            node.Level,
		Position:         node.Position,
		IsActive:         node.IsActive,
		IsTrading:        node.IsTrading,
		AgentLevelID:     int(node.AgentLevelID),
		CommissionEarned: commissionEarned,
		VolumeUsd:        volumeUsd,
		FeeVolumeUsd:     feeVolumeUsd,
		Tree:             ModelInfiniteAgentReferralTreeToGQL(&node.Tree),
		User:             utils.Translate[gql_model.User](&node.User),
		AgentLevel:       utils.Translate[gql_model.AgentLevel](&node.AgentLevel),
	}

	if node.ParentUserID != nil {
		parentUserID := node.ParentUserID.String()
		gqlNode.ParentUserID = &parentUserID
	}

	if node.ReferrerID != nil {
		referrerID := node.ReferrerID.String()
		gqlNode.ReferrerID = &referrerID
	}

	if node.ParentUser != nil {
		gqlNode.ParentUser = utils.Translate[gql_model.User](node.ParentUser)
	}

	if node.Referrer != nil {
		gqlNode.Referrer = utils.Translate[gql_model.User](node.Referrer)
	}

	return gqlNode
}
