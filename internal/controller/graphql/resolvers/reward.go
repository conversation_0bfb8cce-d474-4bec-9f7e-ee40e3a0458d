package resolvers

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/invitation"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/reward"
)

type RewardResolver struct {
	rewardService           *reward.RewardDataService
	invitationRecordService *invitation.InvitationRecordService
}

func NewRewardResolver() *RewardResolver {
	return &RewardResolver{
		rewardService:           reward.NewRewardDataService(),
		invitationRecordService: invitation.NewInvitationRecordService(),
	}
}

func (r *RewardResolver) RewardData(ctx context.Context) (*gql_model.RewardDataResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.RewardDataResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	rewardData, err := r.rewardService.GetRewardData(ctx, userID)
	if err != nil {
		return &gql_model.RewardDataResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to obtain reward data: %v", err),
		}, nil
	}

	accumulatedRewards, _ := rewardData.AccumulatedRewards.Float64()
	usdcRebate, _ := rewardData.USDCRebate.Float64()
	solRebate, _ := rewardData.SOLRebate.Float64()

	gqlRewardData := &gql_model.RewardData{
		AccumulatedRewards:        accumulatedRewards,
		UsdcRebate:                usdcRebate,
		SolRebate:                 solRebate,
		InvitationTransactionRank: rewardData.InvitationTransactionRank,
		InvitationRevenueRank:     rewardData.InvitationRevenueRank,
		ProgressPrompt:            rewardData.ProgressPrompt,
	}

	return &gql_model.RewardDataResponse{
		Success: true,
		Message: "Successfully obtained reward data",
		Data:    gqlRewardData,
	}, nil
}

func (r *RewardResolver) InvitationRecords(ctx context.Context) (*gql_model.InvitationRecordResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.InvitationRecordResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	invitationRecords, err := r.invitationRecordService.GetInvitationRecords(ctx, userID)
	if err != nil {
		return &gql_model.InvitationRecordResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to obtain invitation record: %v", err),
		}, nil
	}

	var gqlRecords []*gql_model.InvitationRecord
	for _, record := range invitationRecords {
		transactionVolume, _ := record.AddressTransactionVolume.Float64()
		handlingFee, _ := record.AddressHandlingFee.Float64()

		gqlRecord := &gql_model.InvitationRecord{
			AcceptedInvitationAddress:      record.AcceptedInvitationAddress,
			AddressTransactionVolume:       transactionVolume,
			SubordinateInvitedAddressCount: record.SubordinateInvitedAddressCount,
			AddressHandlingFee:             handlingFee,
		}
		gqlRecords = append(gqlRecords, gqlRecord)
	}

	return &gql_model.InvitationRecordResponse{
		Success: true,
		Message: "Get invitation record successfully",
		Data:    gqlRecords,
	}, nil
}
