package resolvers

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/data_overview"
)

type DataOverviewResolver struct {
	s data_overview.DataOverviewServiceInterface
}

func NewDataOverviewResolver() *DataOverviewResolver {
	return &DataOverviewResolver{
		s: data_overview.NewDataOverviewService(),
	}
}

// DataOverview handles the data overview query
func (d *DataOverviewResolver) DataOverview(ctx context.Context, input gql_model.DataOverviewInput) (*gql_model.DataOverviewWithSummary, error) {
	// Get user ID from context
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return nil, fmt.Errorf("user ID not found in context")
	}

	// Convert GraphQL enum to string
	dataType := string(input.DataType)
	timeRange := string(input.TimeRange)

	// Get data overview based on data type and time range
	dataOverviewResponse, err := d.s.GetDataOverview(ctx, userID, dataType, timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to get data overview: %w", err)
	}

	// Convert to GraphQL response
	var message *string
	if dataOverviewResponse.Message != "" {
		message = &dataOverviewResponse.Message
	}

	response := &gql_model.DataOverviewWithSummary{
		Data:    DTODataOverviewListToGQL(dataOverviewResponse.Data),
		Summary: DTODataOverviewSummaryToGQL(dataOverviewResponse.Summary),
		Success: dataOverviewResponse.Success,
		Message: message,
	}

	return response, nil
}

// DTODataOverviewListToGQL converts DTO list to GraphQL model list
func DTODataOverviewListToGQL(dataList []*response.DataOverview) []*gql_model.DataOverview {
	if dataList == nil {
		return nil
	}

	var result []*gql_model.DataOverview
	for _, data := range dataList {
		if data != nil {
			result = append(result, &gql_model.DataOverview{
				RebateAmount:      data.RebateAmount,
				TransactionVolume: data.TransactionVolume,
				InvitationCount:   data.InvitationCount,
				Timestamp:         data.Timestamp,
				Period:            data.Period,
			})
		}
	}

	return result
}

// DTODataOverviewSummaryToGQL converts DTO summary to GraphQL model
func DTODataOverviewSummaryToGQL(summary *response.DataOverviewSummary) *gql_model.DataOverviewSummary {
	if summary == nil {
		return nil
	}

	return &gql_model.DataOverviewSummary{
		TotalRebateAmount:        summary.TotalRebateAmount,
		TotalTransactionVolume:   summary.TotalTransactionVolume,
		TotalInvitationCount:     summary.TotalInvitationCount,
		PeakRebateAmount:         summary.PeakRebateAmount,
		PeakTransactionVolume:    summary.PeakTransactionVolume,
		PeakInvitationCount:      summary.PeakInvitationCount,
		AverageRebateAmount:      summary.AverageRebateAmount,
		AverageTransactionVolume: summary.AverageTransactionVolume,
		AverageInvitationCount:   summary.AverageInvitationCount,
	}
}
