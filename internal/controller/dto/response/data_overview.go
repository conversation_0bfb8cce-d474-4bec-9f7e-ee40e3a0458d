package response

import "time"

// DataOverview represents data overview response
type DataOverview struct {
	RebateAmount      float64   `json:"rebateAmount"`
	TransactionVolume float64   `json:"transactionVolume"`
	InvitationCount   int       `json:"invitationCount"`
	Timestamp         time.Time `json:"timestamp"`
	Period            string    `json:"period"`
}

// DataOverviewSummary represents summary statistics for data overview
type DataOverviewSummary struct {
	TotalRebateAmount        float64 `json:"totalRebateAmount"`
	TotalTransactionVolume   float64 `json:"totalTransactionVolume"`
	TotalInvitationCount     int     `json:"totalInvitationCount"`
	PeakRebateAmount         float64 `json:"peakRebateAmount"`
	PeakTransactionVolume    float64 `json:"peakTransactionVolume"`
	PeakInvitationCount      int     `json:"peakInvitationCount"`
	AverageRebateAmount      float64 `json:"averageRebateAmount"`
	AverageTransactionVolume float64 `json:"averageTransactionVolume"`
	AverageInvitationCount   float64 `json:"averageInvitationCount"`
}

// DataOverviewResponse represents the response for data overview query
type DataOverviewResponse struct {
	Data    []*DataOverview      `json:"data"`
	Summary *DataOverviewSummary `json:"summary"`
	Success bool                 `json:"success"`
	Message string               `json:"message,omitempty"`
}
